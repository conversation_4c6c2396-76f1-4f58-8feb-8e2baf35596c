# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'firebase_admin',
        'firebase_admin.credentials',
        'firebase_admin.firestore',
        'google.cloud.firestore',
        'google.cloud.firestore_v1',
        'google.auth',
        'google.auth.transport',
        'google.auth.transport.requests',
        'grpc',
        'grpcio',
        'cryptography',
        'pyasn1',
        'pyasn1_modules',
        'rsa',
        'cachetools',
        'requests',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'pyaudio',
        'speech_recognition',
        'pyttsx3',
        'openai',
        'anthropic',
        'threading',
        'queue',
        'json',
        'datetime',
        'time',
        'sys',
        'os',
        'pathlib',
        'ctypes',
        'ctypes.wintypes',
        'win32gui',
        'win32con',
        'win32api',
        'pynput',
        'pynput.keyboard',
        'pynput.mouse'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
        'jupyter',
        'notebook',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI_Assistant',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)
