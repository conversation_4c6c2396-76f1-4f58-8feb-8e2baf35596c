from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, Q<PERSON><PERSON>Layout, QHB<PERSON><PERSON><PERSON><PERSON>, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QUrl
from PyQt5.QtGui import QFont, QDesktopServices
from firebase_config import FirebaseManager
import threading
import ctypes
from ctypes import wintypes
import psutil

# Import stealth configuration
try:
    from stealth_config import (
        WINDOW_TRANSPARENCY, ENABLE_SCREEN_CAPTURE_HIDING, ENABLE_TASKBAR_HIDING,
        ENABLE_CLICK_THROUGH_DEFAULT, ADVANCED_STEALTH_MODE, AUTO_HIDE_ON_SCREEN_SHARE,
        DEBUG_STEALTH_FEATURES
    )
except ImportError:
    # Default values if config file doesn't exist
    WINDOW_TRANSPARENCY = 0.95
    ENABLE_SCREEN_CAPTURE_HIDING = True
    ENABLE_TASKBAR_HIDING = False
    ENABLE_CLICK_THROUGH_DEFAULT = False
    ADVANCED_STEALTH_MODE = False
    AUTO_HIDE_ON_SCREEN_SHARE = False
    DEBUG_STEALTH_FEATURES = True

class UserAuthDialog(QDialog):
    # Signal to emit when user is authenticated
    user_authenticated = pyqtSignal(str, int, dict)  # email, remaining_time, user_data
    # Signal to emit error messages from thread to UI
    error_signal = pyqtSignal(str)  # error_message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.firebase_manager = FirebaseManager()
        self.current_user_email = None
        self.remaining_time = 0
        self.user_data = None
        self._authenticating = False
        self.setup_ui()

        # Setup screen sharing detection timer (only if AUTO_HIDE_ON_SCREEN_SHARE is enabled)
        if AUTO_HIDE_ON_SCREEN_SHARE:
            self.screen_share_timer = QTimer()
            self.screen_share_timer.timeout.connect(self.check_screen_sharing)
            self.screen_share_timer.start(5000)  # Check every 5 seconds like main window
        
    def setup_ui(self):
        """Setup the authentication dialog UI"""
        self.setWindowTitle("AI Assistant - Login")
        self.setFixedSize(600, 400)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)

        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(25)
        layout.setContentsMargins(40, 40, 40, 40)

        # Title with improved typography
        title_label = QLabel("AI Assistant Login")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #1976D2;
                margin-bottom: 10px;
                background-color: transparent;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                letter-spacing: 0.5px;
            }
        """)
        layout.addWidget(title_label)

        # Subtitle with smaller text
        subtitle_label = QLabel("Enter your email address to access the AI Assistant")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Segoe UI", 10))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #6C757D;
                margin-bottom: 35px;
                background-color: transparent;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: 400;
                line-height: 1.4;
            }
        """)
        layout.addWidget(subtitle_label)
        
        # Email input section with modern styling
        email_container = QFrame()
        email_container.setStyleSheet("""
            QFrame {
                border: 1px solid #E9ECEF;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
                background-color: #FFFFFF;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }
        """)
        email_layout = QVBoxLayout()
        email_layout.setSpacing(15)
        email_layout.setContentsMargins(0, 0, 0, 0)

        # Email label with thinner styling
        email_label = QLabel("Email Address")
        email_label.setFont(QFont("Segoe UI", 12, QFont.Normal))
        email_label.setStyleSheet("""
            QLabel {
                color: #495057;
                margin-bottom: 8px;
                background-color: transparent;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: 400;
            }
        """)
        email_layout.addWidget(email_label)

        # Email input with enhanced styling
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter your email address")
        self.email_input.setFont(QFont("Segoe UI", 14))
        self.email_input.setFixedHeight(52)

        # Ensure proper focus policy and interaction
        self.email_input.setFocusPolicy(Qt.StrongFocus)
        self.email_input.setEnabled(True)

        # Add real-time validation
        self.email_input.textChanged.connect(self.validate_email_input)

        # Add keyboard navigation support
        self.email_input.returnPressed.connect(self.authenticate_user)

        # Add accessibility attributes
        self.email_input.setAccessibleName("Email Address")
        self.email_input.setAccessibleDescription("Enter your email address to sign in")

        self.email_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #DEE2E6;
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                background-color: #FFFFFF;
                color: #212529;
                font-family: 'Segoe UI', sans-serif;
            }
            QLineEdit:focus {
                border-color: #1976D2;
                background-color: #F8F9FF;
                outline: none;
                box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            }
            QLineEdit:hover {
                border-color: #ADB5BD;
                background-color: #F8F9FA;
            }
            QLineEdit::placeholder {
                color: #6C757D;
                font-style: italic;
            }
        """)

        # Override focus events to ensure proper focus handling
        original_focus_in = self.email_input.focusInEvent
        original_focus_out = self.email_input.focusOutEvent

        def custom_focus_in(event):
            original_focus_in(event)
            print("✅ Email input gained focus")

        def custom_focus_out(event):
            original_focus_out(event)
            print("⚠️ Email input lost focus")

        self.email_input.focusInEvent = custom_focus_in
        self.email_input.focusOutEvent = custom_focus_out

        email_layout.addWidget(self.email_input)

        email_container.setLayout(email_layout)
        layout.addWidget(email_container)
        
        # Status label with improved styling
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Segoe UI", 12))
        self.status_label.setMinimumHeight(45)
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6C757D;
                margin: 20px 0;
                background-color: transparent;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-weight: 400;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.status_label)

        # Buttons container with improved alignment
        button_container = QFrame()
        button_container.setStyleSheet("QFrame { background-color: transparent; border: none; }")
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setAlignment(Qt.AlignCenter)

        # Login button with enhanced styling
        self.login_button = QPushButton("Login")
        self.login_button.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.login_button.setFixedHeight(50)
        self.login_button.setFixedWidth(150)
        self.login_button.setCursor(Qt.PointingHandCursor)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #1976D2;
                color: #FFFFFF;
                border: none;
                border-radius: 10px;
                padding: 14px 28px;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #1565C0;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
            }
            QPushButton:pressed {
                background-color: #0D47A1;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """)
        self.login_button.clicked.connect(self.authenticate_user)
        button_layout.addWidget(self.login_button)

        # Cancel button with matching styling
        cancel_button = QPushButton("Cancel")
        cancel_button.setFont(QFont("Segoe UI", 14))
        cancel_button.setFixedHeight(50)
        cancel_button.setFixedWidth(150)
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F8F9FA;
                color: #495057;
                border: 2px solid #DEE2E6;
                border-radius: 10px;
                padding: 14px 28px;
                font-size: 14px;
                font-weight: 500;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #E9ECEF;
                border-color: #ADB5BD;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            QPushButton:pressed {
                background-color: #DEE2E6;
                transform: translateY(0px);
            }
        """)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        button_container.setLayout(button_layout)
        layout.addWidget(button_container)

        # Contact info with only name (WhatsApp on click)
        contact_label = QLabel('Need more time? Contact <a href="#whatsapp" style="color: #1976D2; text-decoration: underline;">Abid Ansari</a>')
        contact_label.setAlignment(Qt.AlignCenter)
        contact_label.setFont(QFont("Segoe UI", 11))
        contact_label.setWordWrap(True)
        contact_label.setStyleSheet("""
            QLabel {
                color: #616161;
                margin-top: 25px;
                background-color: transparent;
                text-align: center;
            }
            QLabel a {
                color: #1976D2;
                text-decoration: underline;
                font-weight: 500;
            }
            QLabel a:hover {
                color: #0D47A1;
                text-decoration: underline;
                background-color: rgba(25, 118, 210, 0.1);
                padding: 2px 4px;
                border-radius: 3px;
            }
        """)
        contact_label.linkActivated.connect(self.open_whatsapp)
        layout.addWidget(contact_label)
        
        self.setLayout(layout)

        # Apply modern dialog styling
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid #DEE2E6;
                border-radius: 16px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            }
        """)

        # Remove transparency attribute to make it solid
        self.setAttribute(Qt.WA_TranslucentBackground, False)

        # Focus on email input - set focus multiple times to ensure it works
        self.email_input.setFocus()

        # Force focus with Qt.OtherFocusReason to ensure it's properly set
        self.email_input.setFocus(Qt.OtherFocusReason)

        # Ensure email input is enabled and can receive focus
        self.email_input.setEnabled(True)
        self.email_input.setFocusPolicy(Qt.StrongFocus)

        # Setup focus enforcement timer (less frequent to reduce CPU usage)
        self.focus_timer = QTimer()
        self.focus_timer.timeout.connect(self.enforce_focus)
        self.focus_timer.start(1000)  # Check every 1 second to reduce interference

        # Initialize validation state
        self.is_email_valid = False
        self.validation_timer = QTimer()
        self.validation_timer.setSingleShot(True)
        self.validation_timer.timeout.connect(self.perform_validation)

        # Connect signals
        self.error_signal.connect(self._handle_auth_error)
        self.user_authenticated.connect(self._handle_authentication_success)

        # Add keyboard shortcuts for better accessibility
        self.setup_keyboard_shortcuts()

        # Set tab order for proper navigation
        self.setTabOrder(self.email_input, self.login_button)
        self.setTabOrder(self.login_button, cancel_button)

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for better accessibility"""
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        # Enter key on login button
        login_shortcut = QShortcut(QKeySequence("Return"), self.login_button)
        login_shortcut.activated.connect(self.authenticate_user)

        # Escape key to cancel
        escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        escape_shortcut.activated.connect(self.reject)

        # Ctrl+A to select all text in email input
        select_all_shortcut = QShortcut(QKeySequence("Ctrl+A"), self.email_input)
        select_all_shortcut.activated.connect(self.email_input.selectAll)

    def center_on_screen(self):
        """Center the dialog on the screen"""
        from PyQt5.QtWidgets import QDesktopWidget

        # Get screen geometry
        screen = QDesktopWidget().screenGeometry()

        # Calculate center position
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2

        # Move dialog to center
        self.move(x, y)

    def keyPressEvent(self, event):
        """Handle key press events for better UX"""
        from PyQt5.QtCore import Qt

        # Handle Enter key
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if self.login_button.isEnabled():
                self.authenticate_user()
            return

        # Handle Escape key
        if event.key() == Qt.Key_Escape:
            if not (hasattr(self, '_authenticating') and self._authenticating):
                self.reject()
            return

        # Call parent implementation
        super().keyPressEvent(event)

    def validate_email_input(self):
        """Real-time email validation with visual feedback"""
        # Debounce validation to avoid excessive calls
        self.validation_timer.stop()
        self.validation_timer.start(300)  # Wait 300ms after user stops typing

    def perform_validation(self):
        """Perform actual email validation and update UI"""
        email = self.email_input.text().strip()

        if not email:
            # Empty input - neutral state
            self.is_email_valid = False
            self.update_input_validation_state("neutral")
            self.login_button.setEnabled(False)
            return

        # Email format validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid_format = re.match(email_pattern, email.lower())

        if is_valid_format:
            self.is_email_valid = True
            self.update_input_validation_state("valid")
            self.login_button.setEnabled(True)
        else:
            self.is_email_valid = False
            self.update_input_validation_state("invalid")
            self.login_button.setEnabled(False)

    def update_input_validation_state(self, state):
        """Update input field styling based on validation state - subtle changes"""
        if state == "valid":
            self.email_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #4CAF50;
                    border-radius: 10px;
                    padding: 14px 18px;
                    font-size: 14px;
                    background-color: #FFFFFF;
                    color: #212529;
                    font-family: 'Segoe UI', sans-serif;
                }
                QLineEdit:focus {
                    border-color: #4CAF50;
                    background-color: #F8F9FF;
                    outline: none;
                    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
                }
                QLineEdit:hover {
                    border-color: #4CAF50;
                    background-color: #F8F9FA;
                }
                QLineEdit::placeholder {
                    color: #6C757D;
                    font-style: italic;
                }
            """)
        elif state == "invalid":
            self.email_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #F44336;
                    border-radius: 10px;
                    padding: 14px 18px;
                    font-size: 14px;
                    background-color: #FFFFFF;
                    color: #212529;
                    font-family: 'Segoe UI', sans-serif;
                }
                QLineEdit:focus {
                    border-color: #F44336;
                    background-color: #F8F9FF;
                    outline: none;
                    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
                }
                QLineEdit:hover {
                    border-color: #F44336;
                    background-color: #F8F9FA;
                }
                QLineEdit::placeholder {
                    color: #6C757D;
                    font-style: italic;
                }
            """)
        else:  # neutral - original styling
            self.email_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #DEE2E6;
                    border-radius: 10px;
                    padding: 14px 18px;
                    font-size: 14px;
                    background-color: #FFFFFF;
                    color: #212529;
                    font-family: 'Segoe UI', sans-serif;
                }
                QLineEdit:focus {
                    border-color: #1976D2;
                    background-color: #F8F9FF;
                    outline: none;
                    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
                }
                QLineEdit:hover {
                    border-color: #ADB5BD;
                    background-color: #F8F9FA;
                }
                QLineEdit::placeholder {
                    color: #6C757D;
                    font-style: italic;
                }
            """)

    def reject(self):
        """Override reject to prevent closing during authentication"""
        if hasattr(self, '_authenticating') and self._authenticating:
            print("⚠️ Cannot close dialog during authentication")
            return
        print("❌ User cancelled authentication")
        super().reject()

    def closeEvent(self, event):
        """Override close event to prevent closing during authentication"""
        if hasattr(self, '_authenticating') and self._authenticating:
            print("⚠️ Cannot close dialog during authentication")
            event.ignore()
            return
        print("❌ Dialog closing")
        super().closeEvent(event)



    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for better UX"""
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        # Escape key to close dialog
        escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        escape_shortcut.activated.connect(self.reject)

        # Ctrl+A to select all text in email input
        select_all_shortcut = QShortcut(QKeySequence("Ctrl+A"), self)
        select_all_shortcut.activated.connect(lambda: self.email_input.selectAll())

        # F5 to refresh/clear and focus email input
        refresh_shortcut = QShortcut(QKeySequence("F5"), self)
        refresh_shortcut.activated.connect(self.refresh_dialog)

    def refresh_dialog(self):
        """Refresh dialog - clear email and focus"""
        self.email_input.clear()
        self.email_input.setFocus()
        self.status_label.setText("")
        print("🔄 Dialog refreshed")

    def apply_stealth_features(self):
        """Apply stealth features based on configuration - consistent with main window"""
        if DEBUG_STEALTH_FEATURES:
            print("🥷 Applying stealth features to auth dialog...")

        # Show the window first
        self.show()

        # Small delay to ensure window is fully rendered
        import time
        time.sleep(0.1)

        # Hide from screen capture (key stealth feature)
        if ENABLE_SCREEN_CAPTURE_HIDING:
            self.hide_from_capture()
            if DEBUG_STEALTH_FEATURES:
                print("🔒 Auth dialog hidden from screen capture")

        # Hide from taskbar if configured
        if ENABLE_TASKBAR_HIDING:
            self.hide_from_taskbar()
            if DEBUG_STEALTH_FEATURES:
                print("📋 Auth dialog hidden from taskbar")

        # Set window transparency (same as main window)
        self.setWindowOpacity(WINDOW_TRANSPARENCY)
        if DEBUG_STEALTH_FEATURES:
            print(f"👻 Auth dialog transparency set to {WINDOW_TRANSPARENCY}")

        # Apply advanced stealth mode if enabled
        if ADVANCED_STEALTH_MODE:
            self.apply_advanced_stealth()

        # Ensure focus is on email input after stealth features
        self.ensure_proper_focus()

        if DEBUG_STEALTH_FEATURES:
            print("✅ Auth dialog stealth features applied successfully")

    def ensure_proper_focus(self):
        """Ensure proper focus on email input with multiple attempts"""
        # Multiple focus attempts to ensure it works
        self.email_input.setFocus()
        self.email_input.setFocus(Qt.OtherFocusReason)

        # Force activation of the window to ensure focus works
        self.activateWindow()
        self.raise_()

        # Final focus attempt with a small delay
        QTimer.singleShot(100, lambda: self.email_input.setFocus(Qt.OtherFocusReason))

        # Additional focus enforcement after stealth features
        QTimer.singleShot(200, lambda: (
            self.email_input.setFocus(),
            self.activateWindow()
        ))

    def showEvent(self, event):
        """Override showEvent to ensure focus is properly set when dialog is shown"""
        super().showEvent(event)

        # Set focus on email input when dialog is shown
        QTimer.singleShot(50, lambda: self.email_input.setFocus(Qt.OtherFocusReason))
        QTimer.singleShot(100, lambda: self.email_input.setFocus(Qt.OtherFocusReason))

        # Ensure window is active
        self.activateWindow()
        self.raise_()

    def enforce_focus(self):
        """Improved focus enforcement with better conditions"""
        try:
            # Only restore focus if all conditions are met
            if (self.isVisible() and
                self.email_input.isEnabled() and
                not hasattr(self, '_authenticating') or not self._authenticating):

                # Check if focus is not on any important widget
                focused_widget = self.focusWidget()
                important_widgets = [self.email_input, self.login_button]

                if focused_widget not in important_widgets:
                    self.email_input.setFocus(Qt.OtherFocusReason)
                    self.activateWindow()
                    self.raise_()
                    print("🔄 Focus restored to email input")

        except Exception as e:
            print(f"❌ Error enforcing focus: {e}")

    def hide_from_capture(self):
        """Enhanced hide window from screen capture - Windows 10 optimized"""
        try:
            hwnd = int(self.winId())

            # Method 1: Set window to be excluded from capture (Windows 10+)
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result1 = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            # Method 2: Set window as layered and make it invisible to capture
            GWL_EXSTYLE = -20
            WS_EX_LAYERED = 0x00080000
            WS_EX_TOOLWINDOW = 0x00000080

            # Get current extended style
            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)

            # Add styles for hiding but keep window interactive (removed WS_EX_NOACTIVATE)
            new_style = current_style | WS_EX_LAYERED | WS_EX_TOOLWINDOW
            result2 = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

            # Method 3: Windows 10 DWM cloaking
            DWM_CLOAKED = 14
            cloaked_value = ctypes.c_int(1)
            result3 = False
            try:
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd, DWM_CLOAKED, ctypes.byref(cloaked_value), ctypes.sizeof(cloaked_value)
                )
                result3 = True
            except:
                pass  # DWM API might not be available

            # Store hwnd for later use
            self.hwnd = hwnd

            success_count = sum([result1, result2, result3])
            if success_count >= 2:
                print("✅ Auth dialog successfully hidden from screen capture (Windows 10 Enhanced)")
                print(f"   • {success_count}/3 hiding methods applied successfully")
                print("   • Display affinity, layered window, and DWM cloaking active")
            else:
                print(f"⚠️ Partial success hiding auth dialog from capture ({success_count}/3 methods)")

        except Exception as e:
            print(f"❌ Error hiding auth dialog from capture: {e}")

    def hide_from_taskbar(self):
        """Hide window from taskbar"""
        try:
            if ENABLE_TASKBAR_HIDING:
                hwnd = int(self.winId())

                # Hide from taskbar and Alt+Tab (but keep window interactive)
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW
                ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if DEBUG_STEALTH_FEATURES:
                    print("✅ Auth dialog hidden from taskbar")
        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error hiding auth dialog from taskbar: {e}")

    def apply_advanced_stealth(self):
        """Apply advanced stealth techniques (but keep window interactive)"""
        try:
            hwnd = int(self.winId())

            # Make window non-enumerable but keep it interactive
            GWL_EXSTYLE = -20
            WS_EX_TOOLWINDOW = 0x00000080

            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
            # Removed WS_EX_NOACTIVATE to keep window interactive
            new_style = current_style | WS_EX_TOOLWINDOW

            result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

            # Don't set window to bottom of Z-order for login dialog
            # as it needs to be accessible to user

            if DEBUG_STEALTH_FEATURES:
                print("🔒 Auth dialog: Advanced stealth mode applied (interactive)")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying advanced stealth to auth dialog: {e}")

    def check_screen_sharing(self):
        """Enhanced screen sharing detection for Windows 10 compatibility"""
        try:
            # Expanded screen sharing process names for better detection
            screen_share_processes = [
                'zoom.exe', 'teams.exe', 'skype.exe', 'discord.exe',
                'obs64.exe', 'obs32.exe', 'streamlabs obs.exe',
                'webexmta.exe', 'gotomeeting.exe', 'anydesk.exe',
                'teamviewer.exe', 'chrome.exe', 'firefox.exe',
                'msedge.exe', 'opera.exe', 'brave.exe', 'vivaldi.exe',
                'microsoftedge.exe', 'microsoftedgecp.exe',
                'webex.exe', 'slack.exe', 'whatsapp.exe',
                'telegram.exe', 'signal.exe', 'meet.exe'
            ]

            # Check if any screen sharing process is running
            screen_sharing_detected = False
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    process_name = proc.info['name'].lower()
                    if any(share_proc in process_name for share_proc in screen_share_processes):
                        screen_sharing_detected = True
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Note: Unlike main window, auth dialog doesn't need enhanced hiding
            # because it already has proper screen capture hiding applied
            # and should remain visible to user at all times

        except Exception as e:
            print(f"❌ Error checking screen sharing for auth dialog: {e}")

    def closeEvent(self, event):
        """Clean up when dialog is closed"""
        try:
            # Stop all timers
            if hasattr(self, 'screen_share_timer'):
                self.screen_share_timer.stop()

            if hasattr(self, 'focus_timer'):
                self.focus_timer.stop()



        except Exception as e:
            print(f"❌ Error during auth dialog cleanup: {e}")

        super().closeEvent(event)

    def open_whatsapp(self, link):
        """Open WhatsApp with Abid Ansari's number"""
        try:
            # WhatsApp number (remove + and spaces)
            phone_number = "918104184175"
            whatsapp_url = f"https://wa.me/{phone_number}?text=Hi%20Abid,%20I%20need%20more%20time%20for%20AI%20Assistant%20access."

            # Open WhatsApp in default browser
            QDesktopServices.openUrl(QUrl(whatsapp_url))
            print(f"✅ Opening WhatsApp for {phone_number}")

        except Exception as e:
            print(f"❌ Error opening WhatsApp: {e}")
            # Fallback: try to open phone dialer
            try:
                QDesktopServices.openUrl(QUrl("tel:+918104184175"))
            except:
                pass
    
    def authenticate_user(self):
        """Authenticate user with Firebase - Enhanced validation and error handling"""
        try:
            # Prevent multiple simultaneous authentication attempts
            if hasattr(self, '_authenticating') and self._authenticating:
                print("⚠️ Authentication already in progress, ignoring click")
                return

            email = self.email_input.text().strip().lower()

            # Enhanced validation with better user feedback
            if not email:
                self.show_validation_error("Please enter your email address")
                self.email_input.setFocus()
                return

            # Use the real-time validation result
            if not self.is_email_valid:
                self.show_validation_error("Please enter a valid email address (e.g., <EMAIL>)")
                self.email_input.setFocus()
                self.email_input.selectAll()
                return

            print(f"🔍 Starting authentication for: {email}")

            # Set authenticating flag FIRST to prevent multiple clicks
            self._authenticating = True

            # Update UI for authentication state
            self.login_button.setEnabled(False)
            self.email_input.setEnabled(False)
            self.show_loading_state("Signing in...")

            # Run authentication in separate thread
            threading.Thread(target=self._authenticate_thread, args=(email,), daemon=True).start()

        except Exception as e:
            print(f"❌ Error in authenticate_user: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("Authentication error occurred. Please try again.")
            self._re_enable_ui()

    def show_validation_error(self, message):
        """Show validation error with appropriate styling"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #D32F2F;
                font-weight: bold;
                background-color: #FFEBEE;
                border: 1px solid #FFCDD2;
                border-radius: 4px;
                padding: 8px;
                margin: 5px;
            }
        """)

        # Clear error after 5 seconds
        QTimer.singleShot(5000, lambda: self.status_label.setText(""))

    def show_loading_state(self, message):
        """Show loading state with appropriate styling"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #1976D2;
                font-weight: 500;
                background-color: transparent;
            }
        """)


    
    def _authenticate_thread(self, email):
        """Authentication thread to avoid blocking UI - Improved error handling"""
        try:
            print(f"🔍 Authenticating user: {email}")

            # Add small delay to show authentication feedback
            import time
            time.sleep(0.5)

            # Check if Firebase manager exists
            if not hasattr(self, 'firebase_manager') or self.firebase_manager is None:
                print("❌ Firebase manager not initialized")
                self.error_signal.emit("❌ Authentication service not available.\nPlease restart the application.")
                return

            is_valid, remaining_time, user_data = self.firebase_manager.validate_user(email)

            print(f"🔍 Validation result: is_valid={is_valid}, remaining_time={remaining_time}, user_data={user_data}")

            # Update UI in main thread using QTimer.singleShot
            if is_valid:
                print(f"✅ Authentication successful for {email}")
                # Store data and emit signal
                self._success_data = (email, remaining_time, user_data)
                self.user_authenticated.emit(email, remaining_time, user_data)
            else:
                print(f"❌ Authentication failed for {email}")
                if user_data is None:
                    # User not found
                    error_msg = "❌ User not found in database.\nPlease check your email or contact Abid Ansari"
                    print(f"🚨 User not found error: {error_msg}")
                    # Emit signal to show error in UI thread
                    self.error_signal.emit(error_msg)
                else:
                    # User found but no time remaining
                    error_msg = "❌ No time remaining for this account.\nContact Abid Ansari for more time"
                    print(f"🚨 No time remaining error: {error_msg}")
                    # Emit signal to show error in UI thread
                    self.error_signal.emit(error_msg)

        except Exception as e:
            print(f"❌ Authentication exception: {e}")
            import traceback
            traceback.print_exc()
            error_msg = f"❌ Connection error: {str(e)}\nPlease check your internet connection"
            print(f"🚨 Exception error: {error_msg}")
            # Emit signal to show error in UI thread
            self.error_signal.emit(error_msg)
        finally:
            # Re-enable UI - just set flag, UI will be handled by main thread
            print("🔄 Re-enabling UI...")
            self._authenticating = False

    def _handle_authentication_success(self, email, remaining_time, user_data):
        """Handle authentication success signal"""
        # Re-enable UI first
        self._re_enable_ui()

        # Show success and close
        self._handle_successful_auth(email, remaining_time, user_data)

    def _handle_successful_auth(self, email, remaining_time, user_data):
        """Handle successful authentication in main thread with improved feedback"""
        print(f"✅ Handling successful auth for {email}")
        self.current_user_email = email
        self.remaining_time = remaining_time
        self.user_data = user_data

        # Clear authenticating flag
        self._authenticating = False

        # Show success message with better formatting
        minutes = remaining_time // 60
        hours = minutes // 60
        remaining_minutes = minutes % 60

        if hours > 0:
            time_text = f"{hours}h {remaining_minutes}m"
        else:
            time_text = f"{minutes} minutes"

        self.status_label.setText(f"✅ Welcome!.")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2E7D32;
                font-weight: bold;
                background-color: #E8F5E8;
                border: 1px solid #C8E6C9;
                border-radius: 4px;
                padding: 8px;
                margin: 5px;
            }
        """)

        # Force update
        self.status_label.update()
        self.status_label.repaint()

        # Close dialog after a brief delay with smooth transition
        QTimer.singleShot(2000, self.accept)



    def _handle_auth_error(self, error_message):
        """Handle authentication error in main thread"""
        try:
            print(f"🚨 Handling auth error in main thread: {error_message}")

            # Re-enable UI first
            self._re_enable_ui()

            # Show error message
            self.show_error(error_message)

            # Ensure email input gets focus back
            self.email_input.setFocus()
            self.email_input.selectAll()

        except Exception as e:
            print(f"❌ Error in _handle_auth_error: {e}")
            # Fallback: just re-enable UI
            self._re_enable_ui()

    def _re_enable_ui(self):
        """Re-enable UI elements in main thread with proper restoration"""
        print("🔄 Re-enabling UI elements...")

        print("🔄 Re-enabling UI elements...")
        self._authenticating = False  # Clear authenticating flag
        self.login_button.setEnabled(True)
        self.email_input.setEnabled(True)
        self.email_input.setFocus(Qt.OtherFocusReason)
        print("✅ UI elements re-enabled")
    
    def show_error(self, message):
        """Show error message with modern styling"""
        print(f"🚨 Showing error message: {message}")

        # Ensure we're in the main thread
        if not hasattr(self, 'status_label'):
            print("❌ Status label not found!")
            return

        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #D32F2F;
                font-weight: bold;
                background-color: #FFEBEE;
                border: 1px solid #FFCDD2;
                border-radius: 4px;
                padding: 8px;
                margin: 5px;
            }
        """)

        # Force update
        self.status_label.update()
        self.status_label.repaint()

        print(f"✅ Error message set to status label: {self.status_label.text()}")

        # Clear error after 10 seconds
        QTimer.singleShot(10000, lambda: (
            self.status_label.setText(""),
            print("🔄 Error message cleared")
        ))
    
    def get_user_info(self):
        """Get current user information"""
        return self.current_user_email, self.remaining_time, self.user_data
