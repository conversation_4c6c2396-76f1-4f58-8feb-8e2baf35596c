@echo off
echo 🚀 Interview Assistant - Universal Build Script
echo ==============================================
echo 🐍 Compatible with ALL Python versions (3.7+)
echo.

echo 🔍 Detecting Python installation...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

echo 📦 Installing/upgrading build dependencies...
python -m pip install --upgrade pip setuptools wheel --user
python -m pip install --upgrade pyinstaller --user

echo 📋 Installing application dependencies...
python -m pip install PyQt5 pyaudio SpeechRecognition requests pynput --user

echo 🧹 Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "InterviewAssistant_Distribution" rmdir /s /q "InterviewAssistant_Distribution"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 🔨 Building single standalone executable...
echo ⏳ This may take 2-5 minutes depending on your system...
python build_executable.py

echo.
if exist "dist\InterviewAssistant.exe" (
    echo ✅ Build completed successfully!
    echo 📁 Single executable: dist\InterviewAssistant.exe
    echo 📦 Distribution folder: InterviewAssistant_Distribution\
    echo 🚀 Ready to run on ANY Windows system!
    echo 💡 No Python installation needed on target machines!
) else (
    echo ❌ Build failed! Check error messages above.
    echo 💡 Try running as Administrator if build fails.
)
echo.
pause
