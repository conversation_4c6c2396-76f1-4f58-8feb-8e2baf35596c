#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InterV AI - Live Transcription with AI Response
Optimized for executable build with no console and no temp files
"""

import sys
import os
import io
import threading
import time
import json
import traceback
import tempfile
import atexit
import shutil
import ctypes
from collections import deque
from datetime import datetime

# Suppress console output when running as exe (disabled for debugging)
# if getattr(sys, 'frozen', False):
#     sys.stdout = io.StringIO()
#     sys.stderr = io.StringIO()

try:
    import numpy as np
    import requests
    import pyaudio
    import wave
    import re
    import webbrowser
    import pyautogui
    from PIL import Image, ImageGrab
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                 QWidget, QTextEdit, QLabel, QPushButton, QFrame, QSizeGrip,
                                 QDialog, QLineEdit)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, Q<PERSON>imer, QMetaObject, Q_ARG, QObject
    from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap

    # Keyboard listener imports
    from pynput import keyboard

    # Firebase imports
    import firebase_admin
    from firebase_admin import credentials, firestore

    # Try faster-whisper first (smaller), fallback to openai-whisper
    try:
        from faster_whisper import WhisperModel
        WHISPER_TYPE = "faster"
    except ImportError:
        try:
            import whisper
            WHISPER_TYPE = "openai"
        except ImportError:
            raise ImportError("Neither faster-whisper nor openai-whisper found")



except ImportError as e:
    if not getattr(sys, 'frozen', False):
        print(f"Missing dependency: {e}")
        print("Install with: pip install PyQt5 pyaudio faster-whisper numpy requests firebase-admin")
    sys.exit(1)

# Global variables for temp management
TEMP_DIR = None
AUDIO_BUFFERS = {}  # In-memory audio storage instead of files

# Keyboard Signals Class for CapsLock functionality
class KeyboardSignals(QObject):
    """Qt signals for handling keyboard events in main thread"""
    caps_lock_signal = pyqtSignal()
    screenshot_signal = pyqtSignal()
    submit_question_signal = pyqtSignal(str)  # question text
    clear_text_signal = pyqtSignal()
    delete_char_signal = pyqtSignal()
    add_newline_signal = pyqtSignal()
    add_char_signal = pyqtSignal(str)
    # Selection overlay signals
    show_selection_overlay_signal = pyqtSignal()
    hide_selection_overlay_signal = pyqtSignal()
    resize_selection_signal = pyqtSignal(str, int)  # direction, amount
    move_selection_signal = pyqtSignal(str, int)  # direction, amount

# Selection Overlay Class for Screenshot Selection
class SelectionOverlay(QWidget):
    """Transparent overlay window for screenshot selection"""

    def __init__(self):
        super().__init__()
        self.selection_rect = None
        self.default_width = 400
        self.default_height = 300
        self.min_size = 50
        self.resize_step = 20
        self.setup_overlay()

    def setup_overlay(self):
        """Setup the overlay window properties"""
        # Make window frameless and always on top
        self.setWindowFlags(
            Qt.FramelessWindowHint |
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )

        # Make background transparent
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowOpacity(0.8)

        # Set to fullscreen to cover entire screen
        screen = QApplication.primaryScreen().geometry()
        self.setGeometry(screen)

        # Initialize selection rectangle at screen center
        self.reset_selection_to_center()
        self.hide_from_capture()

    def hide_from_capture(self):
        """Hide window from screen capture"""
        try:
            import ctypes
            hwnd = int(self.winId())
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)
        except Exception as e:
            print(f"⚠️ Could not hide selection overlay from capture: {e}")

    def reset_selection_to_center(self):
        """Reset selection rectangle to cursor position"""
        try:
            cursor_pos = pyautogui.position()
            self.selection_rect = {
                'x': cursor_pos.x - self.default_width // 2,
                'y': cursor_pos.y - self.default_height // 2,
                'width': self.default_width,
                'height': self.default_height
            }

            # Ensure selection stays within screen bounds
            screen = QApplication.primaryScreen().geometry()
            self.selection_rect['x'] = max(0, min(self.selection_rect['x'], screen.width() - self.selection_rect['width']))
            self.selection_rect['y'] = max(0, min(self.selection_rect['y'], screen.height() - self.selection_rect['height']))

        except Exception as e:
            print(f"❌ Error setting selection to cursor: {e}")
            # Fallback to screen center
            screen = QApplication.primaryScreen().geometry()
            center_x = screen.width() // 2
            center_y = screen.height() // 2

            self.selection_rect = {
                'x': center_x - self.default_width // 2,
                'y': center_y - self.default_height // 2,
                'width': self.default_width,
                'height': self.default_height
            }

    def resize_selection(self, direction, amount):
        """Resize the selection rectangle"""
        if not self.selection_rect:
            return

        orig_x = self.selection_rect['x']
        orig_y = self.selection_rect['y']
        orig_width = self.selection_rect['width']
        orig_height = self.selection_rect['height']

        if direction == 'resize_right':
            self.selection_rect['width'] = orig_width + amount
        elif direction == 'resize_left':
            new_width = orig_width + amount
            width_diff = new_width - orig_width
            self.selection_rect['x'] = max(0, orig_x - width_diff)
            self.selection_rect['width'] = new_width
        elif direction == 'resize_down':
            self.selection_rect['height'] = orig_height + amount
        elif direction == 'resize_up':
            new_height = orig_height + amount
            height_diff = new_height - orig_height
            self.selection_rect['y'] = max(0, orig_y - height_diff)
            self.selection_rect['height'] = new_height

        # Ensure selection stays within screen bounds
        screen = QApplication.primaryScreen().geometry()
        self.selection_rect['x'] = max(0, min(self.selection_rect['x'], screen.width() - self.selection_rect['width']))
        self.selection_rect['y'] = max(0, min(self.selection_rect['y'], screen.height() - self.selection_rect['height']))

        self.update()

    def move_selection(self, direction, amount):
        """Move the selection rectangle"""
        if not self.selection_rect:
            return

        screen = QApplication.primaryScreen().geometry()

        if direction == 'up':
            self.selection_rect['y'] = max(0, self.selection_rect['y'] - amount)
        elif direction == 'down':
            max_y = screen.height() - self.selection_rect['height']
            self.selection_rect['y'] = min(max_y, self.selection_rect['y'] + amount)
        elif direction == 'left':
            self.selection_rect['x'] = max(0, self.selection_rect['x'] - amount)
        elif direction == 'right':
            max_x = screen.width() - self.selection_rect['width']
            self.selection_rect['x'] = min(max_x, self.selection_rect['x'] + amount)

        self.update()

    def get_selection_bounds(self):
        """Get selection bounds for screenshot"""
        if not self.selection_rect:
            return None

        left = self.selection_rect['x']
        top = self.selection_rect['y']
        right = left + self.selection_rect['width']
        bottom = top + self.selection_rect['height']

        return (left, top, right, bottom)

    def paintEvent(self, event):
        """Draw the selection rectangle"""
        if not self.selection_rect:
            return

        from PyQt5.QtGui import QPainter, QPen, QBrush
        from PyQt5.QtCore import QRect

        painter = QPainter(self)

        # Draw semi-transparent overlay
        painter.fillRect(self.rect(), QBrush(QColor(0, 0, 0, 100)))

        # Draw selection rectangle
        selection_qrect = QRect(
            self.selection_rect['x'],
            self.selection_rect['y'],
            self.selection_rect['width'],
            self.selection_rect['height']
        )

        # Clear the selection area
        painter.setCompositionMode(QPainter.CompositionMode_Clear)
        painter.fillRect(selection_qrect, QBrush(QColor(0, 0, 0, 0)))

        # Draw selection border
        painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
        pen = QPen(QColor(0, 150, 255), 3)
        painter.setPen(pen)
        painter.drawRect(selection_qrect)

# Firebase Manager Class
class FirebaseManager:
    def __init__(self):
        self.db = None
        self.initialize_firebase()

    def initialize_firebase(self):
        """Initialize Firebase with service account credentials"""
        try:
            # Firebase service account configuration
            firebase_config = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

            # Initialize Firebase Admin SDK
            if not firebase_admin._apps:
                cred = credentials.Certificate(firebase_config)
                firebase_admin.initialize_app(cred)

            # Initialize Firestore
            self.db = firestore.client()
            print("✅ Firebase initialized successfully")

        except Exception as e:
            print(f"❌ Firebase initialization error: {e}")
            self.db = None

    def validate_user(self, email):
        """
        Validate user and return user data with remaining time
        Returns: (is_valid, remaining_time_seconds, user_data)
        """
        try:
            print(f"🔄 Validating user: {email}")

            if not self.db:
                print("❌ Firebase database not initialized")
                return False, 0, None

            # Get user document from Firestore
            print(f"🔄 Fetching user document for: {email}")
            user_ref = self.db.collection('users').document(email)
            user_doc = user_ref.get(timeout=10)  # Add timeout

            if not user_doc.exists:
                print(f"❌ User {email} not found in database")
                return False, 0, None

            user_data = user_doc.to_dict()
            print(f"🔄 User data retrieved: {user_data}")

            # Check if user is active
            status = user_data.get('status', 'inactive')
            if status != 'active':
                print(f"❌ User {email} status is: {status}")
                return False, 0, user_data

            # Calculate remaining time - use correct Firebase field names
            allocated_minutes = user_data.get('allocated_time_minutes', 0)
            used_minutes = user_data.get('used_minutes', 0)  # This is the correct field name

            remaining_minutes = allocated_minutes - used_minutes

            print(f"🔄 Time calculation: allocated={allocated_minutes}, used={used_minutes}, remaining={remaining_minutes}")
            print(f"🔄 Available fields: {list(user_data.keys())}")

            if remaining_minutes <= 0:
                print(f"❌ No time remaining for user {email}")
                return False, 0, user_data

            # Update last login and ensure used_time_minutes field exists
            try:
                update_data = {
                    'last_login': firestore.SERVER_TIMESTAMP
                }

                # If only old 'used_minutes' field exists, copy to new field
                if 'used_time_minutes' not in user_data and 'used_minutes' in user_data:
                    update_data['used_time_minutes'] = user_data.get('used_minutes', 0)
                    print(f"🔄 Migrating used_minutes to used_time_minutes: {user_data.get('used_minutes', 0)}")

                user_ref.update(update_data)
                print(f"✅ Updated last login for {email}")
            except Exception as e:
                print(f"⚠️ Could not update last login: {e}")

            remaining_seconds = remaining_minutes * 60
            print(f"✅ User {email} validated. Remaining time: {remaining_minutes} minutes ({remaining_seconds} seconds)")
            return True, remaining_seconds, user_data

        except Exception as e:
            print(f"❌ Error validating user {email}: {e}")
            import traceback
            traceback.print_exc()
            return False, 0, None

# Authentication Dialog Class
class UserAuthDialog(QDialog):
    user_authenticated = pyqtSignal(str, int, dict)  # email, remaining_time, user_data
    auth_result_ready = pyqtSignal(bool, int, object, str)  # is_valid, remaining_time, user_data, email

    def __init__(self, parent=None):
        super().__init__(parent)
        self.firebase_manager = FirebaseManager()
        self.is_email_valid = False
        self.setup_ui()
        self.apply_stealth_features()

        # Connect internal signal for thread-safe UI updates
        self.auth_result_ready.connect(self._handle_auth_result)

    def setup_ui(self):
        """Setup the authentication dialog UI"""
        self.setWindowTitle("InterV AI - Login")
        self.setFixedSize(500, 350)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)

        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # Title
        title_label = QLabel("InterV AI Login")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        title_label.setStyleSheet("color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Email input
        email_label = QLabel("Email Address:")
        email_label.setFont(QFont("Segoe UI", 11))
        layout.addWidget(email_label)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter your email address")
        self.email_input.setFont(QFont("Segoe UI", 12))
        self.email_input.setFixedHeight(40)
        self.email_input.textChanged.connect(self.validate_email_input)
        self.email_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: #FFFFFF;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1976D2;
                background-color: #F8F9FF;
            }
        """)
        layout.addWidget(self.email_input)

        # Buttons layout
        button_layout = QHBoxLayout()

        # Contact button
        self.contact_button = QPushButton("Contact Support")
        self.contact_button.setFont(QFont("Segoe UI", 11))
        self.contact_button.setFixedHeight(40)
        self.contact_button.clicked.connect(self.open_whatsapp)
        self.contact_button.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #22C55E;
            }
        """)

        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setFont(QFont("Segoe UI", 11))
        self.login_button.setFixedHeight(40)
        self.login_button.setEnabled(False)
        self.login_button.clicked.connect(self.authenticate_user)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover:enabled {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)

        button_layout.addWidget(self.contact_button)
        button_layout.addWidget(self.login_button)
        layout.addLayout(button_layout)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Segoe UI", 10))
        self.status_label.setWordWrap(True)
        self.status_label.setMinimumHeight(40)
        self.status_label.setMaximumHeight(60)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def apply_stealth_features(self):
        """Apply stealth features to hide from screen sharing"""
        try:
            # Windows-specific stealth features
            if sys.platform == "win32":
                import ctypes
                from ctypes import wintypes

                hwnd = int(self.winId())

                # Hide from Alt+Tab
                ctypes.windll.user32.SetWindowLongW(
                    hwnd, -20,  # GWL_EXSTYLE
                    ctypes.windll.user32.GetWindowLongW(hwnd, -20) | 0x00000080  # WS_EX_TOOLWINDOW
                )

                # Hide from screen sharing (experimental)
                ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, 0x00000011)  # WDA_EXCLUDEFROMCAPTURE

        except Exception as e:
            pass  # Silently continue if stealth features fail

    def validate_email_input(self):
        """Validate email input"""
        email = self.email_input.text().strip()

        if not email:
            self.is_email_valid = False
            self.login_button.setEnabled(False)
            return

        # Email format validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid_format = re.match(email_pattern, email.lower())

        if is_valid_format:
            self.is_email_valid = True
            self.login_button.setEnabled(True)
        else:
            self.is_email_valid = False
            self.login_button.setEnabled(False)

    def open_whatsapp(self):
        """Open WhatsApp with predefined message"""
        phone_number = "918104184175"
        message = "Hi, I need help with InterV AI login."
        whatsapp_url = f"https://wa.me/{phone_number}?text={message}"
        webbrowser.open(whatsapp_url)

    def authenticate_user(self):
        """Authenticate user with Firebase"""
        try:
            email = self.email_input.text().strip().lower()

            if not email or not self.is_email_valid:
                self.show_error("Enter valid email")
                return

            print(f"🔄 Starting authentication for: {email}")

            self.login_button.setEnabled(False)
            self.email_input.setEnabled(False)
            self.status_label.setText("🔄 Connecting...")
            self.status_label.setStyleSheet("color: #1976D2;")

            # Test Firebase connection first
            if not self.firebase_manager.db:
                print("❌ Firebase not initialized")
                self.show_error("Server connection failed.\nTry again.")
                return

            # Run authentication in thread
            self.auth_thread = threading.Thread(target=self._authenticate_thread, args=(email,), daemon=True)
            self.auth_thread.start()

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            self.show_error("Authentication error.\nTry again.")

    def _authenticate_thread(self, email):
        """Authentication thread with proper error handling"""
        try:
            print(f"🔄 Validating user: {email}")
            self.status_label.setText("🔄 Validating...")

            # Add timeout for Firebase call
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("Firebase call timed out")

            # Set timeout (only on non-Windows or handle differently)
            try:
                if hasattr(signal, 'SIGALRM'):
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(10)  # 10 second timeout
            except:
                pass

            is_valid, remaining_time, user_data = self.firebase_manager.validate_user(email)

            # Clear timeout
            try:
                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)
            except:
                pass

            print(f"🔄 Authentication result: valid={is_valid}, time={remaining_time}")

            # Emit signal for thread-safe UI updates
            print(f"🔄 Emitting auth_result_ready signal...")
            # Convert to int but ensure it's within valid range
            remaining_time_int = max(0, min(int(remaining_time), 2147483647))  # Max 32-bit int
            print(f"🔄 Converting time: {remaining_time} -> {remaining_time_int}")
            self.auth_result_ready.emit(is_valid, remaining_time_int, user_data, email)

        except TimeoutError:
            print("❌ Authentication timeout")
            self.auth_result_ready.emit(False, 0, None, email)
        except Exception as e:
            print(f"❌ Authentication thread error: {e}")
            import traceback
            traceback.print_exc()
            self.auth_result_ready.emit(False, 0, None, email)

    def _handle_auth_result(self, is_valid, remaining_time, user_data, email):
        """Handle authentication result in main thread"""
        try:
            print(f"🔄 Handling auth result: is_valid={is_valid}, time={remaining_time}, email={email}")

            if is_valid and remaining_time > 0:
                print(f"✅ Authentication successful for {email}")
                self.status_label.setText("✅ Login successful!\nOpening app...")
                self.status_label.setStyleSheet("color: #2E7D32;")

                # Emit signal with user data
                print(f"🔄 Emitting user_authenticated signal...")
                self.user_authenticated.emit(email, int(remaining_time), user_data or {})
                print(f"✅ Signal emitted successfully")

                # Close dialog after short delay using QTimer in main thread
                print(f"🔄 Closing dialog in 1.5 seconds...")
                close_timer = QTimer(self)
                close_timer.setSingleShot(True)
                close_timer.timeout.connect(lambda: self.close_dialog_with_accept())
                close_timer.start(1500)

            else:
                print(f"❌ Authentication failed for {email}")
                if user_data is None:
                    self.show_error("User not found.\nContact support.")
                elif remaining_time <= 0:
                    self.show_error("No time remaining.\nContact support.")
                else:
                    self.show_error("Access denied.\nContact support.")

        except Exception as e:
            print(f"❌ Error handling auth result: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("Authentication error.\nTry again.")

    def show_error(self, message):
        """Show error message and reset UI"""
        try:
            print(f"❌ Showing error: {message}")
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: #D32F2F; font-weight: bold;")
            self.login_button.setEnabled(True)
            self.email_input.setEnabled(True)

            # Auto-clear error after 5 seconds using QTimer in main thread
            clear_timer = QTimer(self)
            clear_timer.setSingleShot(True)
            clear_timer.timeout.connect(lambda: self.status_label.setText(""))
            clear_timer.start(5000)

        except Exception as e:
            print(f"❌ Error in show_error: {e}")

    def close_dialog_with_accept(self):
        """Close dialog with accept status"""
        try:
            print("🔄 Closing dialog with accept status...")
            self.accept()
            print("✅ Dialog closed with accept")
        except Exception as e:
            print(f"❌ Error closing dialog: {e}")

    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if self.login_button.isEnabled():
                self.authenticate_user()
        elif event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

def setup_temp_directory():
    """Create a temporary directory that auto-cleans"""
    global TEMP_DIR
    try:
        TEMP_DIR = tempfile.mkdtemp(prefix="interv_ai_")
        atexit.register(cleanup_temp_directory)
        return TEMP_DIR
    except:
        TEMP_DIR = tempfile.gettempdir()
        return TEMP_DIR

def cleanup_temp_directory():
    """Clean up temporary directory on exit"""
    global TEMP_DIR, AUDIO_BUFFERS
    AUDIO_BUFFERS.clear()  # Clear in-memory buffers
    if TEMP_DIR and os.path.exists(TEMP_DIR) and "interv_ai_" in TEMP_DIR:
        try:
            shutil.rmtree(TEMP_DIR, ignore_errors=True)
        except:
            pass

# Setup temp directory
setup_temp_directory()

# API Configuration (same as ANSARI_AI_V2)
API_KEYS = {
    'gemini': 'AIzaSyDDcWOQ4VLM2irFtaEdvbq21AT2QqzlESg',
    'mistral': '',
    'openrouter': '',
    'openai': 'sk-1234567890abcdef'
}

API_ENDPOINTS = {
    'gemini': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent',
    'mistral': 'https://api.mistral.ai/v1/chat/completions',
    'openrouter': 'https://openrouter.ai/api/v1/chat/completions',
    'openai': 'https://api.openai.com/v1/chat/completions'
}

# System Prompt for Technical Interview Assistant
SYSTEM_PROMPT = """You are a technical interview assistant. Give clear, accurate definitions with practical examples.

MANDATORY RESPONSE FORMAT:
1. First give DETAILED DEFINITION in 3-4 lines explaining concept thoroughly
2. Then add blank line
3. Then write "Example:"
4. Then give CODE with each line separate

CRITICAL FORMATTING RULES:
- Definition must be detailed and comprehensive (3-4 lines minimum)
- Code must have each statement on new line
- Use proper indentation (4 spaces)
- Add blank lines in code for readability
- NEVER write code in paragraph style

EXAMPLE OF CORRECT FORMAT:

Definition: A JavaScript function is a reusable block of code that performs a specific task. Functions help organize code, avoid repetition, and make programs more modular. They can accept parameters as input and return values as output. Functions are fundamental building blocks in JavaScript programming.

Example:
function greetUser(name) {
    let message = "Hello " + name;

    console.log(message);
    return message;
}

greetUser("John");

WRONG FORMAT (DON'T DO THIS):
Definition: A function is code block. Example: function test() { let x = 5; return x; } that creates reusable code.

ALWAYS follow the correct format with detailed definition first, then separate code example."""

# Stealth Configuration
WINDOW_TRANSPARENCY = 0.90
ENABLE_SCREEN_CAPTURE_HIDING = True
ENABLE_TASKBAR_HIDING = False
LOCAL_VISIBILITY_MODE = True
DEBUG_STEALTH_FEATURES = False

class APIClient:
    def __init__(self):
        self.session = requests.Session()
        # Optimize session for faster connections
        self.session.headers.update({
            'Connection': 'keep-alive',
            'User-Agent': 'InterV-AI/1.0'
        })
        # Connection pooling for faster requests
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=2,
            pool_maxsize=5,
            max_retries=1
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        self.last_used_model = "Unknown"

    def get_ai_response(self, text):
        """Get AI response with fallback system and model tracking"""
        # Create focused prompt for technical definitions
        full_prompt = f"{SYSTEM_PROMPT}\n\nTechnical Question: {text}\n\nProvide a clear, concise answer with definition and example:"

        # Try Gemini first
        try:
            api_key = API_KEYS.get('gemini')
            if api_key:
                payload = {
                    "contents": [{"parts": [{"text": full_prompt}]}],
                    "generationConfig": {
                        "temperature": 0.5,      # Slightly lower for faster, focused responses
                        "maxOutputTokens": 800,  # Reduced for faster response while maintaining quality
                        "topK": 3,              # Limited choices for speed
                        "topP": 0.9             # High probability for quality
                    }
                }

                headers = {"Content-Type": "application/json"}
                url = f"{API_ENDPOINTS['gemini']}?key={api_key}"

                response = self.session.post(url, data=json.dumps(payload), headers=headers, timeout=10)
                response.raise_for_status()

                result = response.json()
                if 'candidates' in result and result['candidates']:
                    self.last_used_model = "Gemini"
                    return result['candidates'][0]['content']['parts'][0]['text']
        except Exception as e:
            pass  # Try next model

        # Try Mistral as fallback
        try:
            api_key = API_KEYS.get('mistral')
            if api_key:
                payload = {
                    "model": "mistral-large-latest",
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": text}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 1000
                }

                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

                response = self.session.post(API_ENDPOINTS['mistral'], data=json.dumps(payload), headers=headers, timeout=10)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and result['choices']:
                    self.last_used_model = "Mistral"
                    return result['choices'][0]['message']['content']
        except Exception as e:
            pass  # Try next model

        # Try OpenRouter as fallback
        try:
            api_key = API_KEYS.get('openrouter')
            if api_key:
                payload = {
                    "model": "google/gemini-flash-1.5",
                    "messages": [
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": text}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 1000
                }

                headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

                response = self.session.post(API_ENDPOINTS['openrouter'], data=json.dumps(payload), headers=headers, timeout=10)
                response.raise_for_status()

                result = response.json()
                if 'choices' in result and result['choices']:
                    self.last_used_model = "OpenRouter"
                    return result['choices'][0]['message']['content']
        except Exception as e:
            pass

        # If all models fail
        self.last_used_model = "Error"
        return "All AI models are currently unavailable. Please try again later."

    def get_last_used_model(self):
        """Get the name of the last successfully used model"""
        return self.last_used_model

class AudioSignals(QThread):
    """Signals for audio processing"""
    text_updated = pyqtSignal(str)
    final_text = pyqtSignal(str)
    ai_response = pyqtSignal(str)

class RealTimeTranscriber(QThread):
    # Signals for GUI updates
    text_updated = pyqtSignal(str)
    final_text = pyqtSignal(str)
    ai_response = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        try:
            # Audio configuration (optimized for faster response while maintaining accuracy)
            self.CHUNK = 1024              # Keep stable chunk size
            self.FORMAT = pyaudio.paInt16
            self.CHANNELS = 1
            self.RATE = 16000
            self.SILENCE_THRESHOLD = 200
            self.SILENCE_DURATION = 1.8    # Slightly faster silence detection
            self.CHUNK_DURATION = 0.9      # More frequent processing

            # Initialize PyAudio
            self.audio = pyaudio.PyAudio()

            # Load Whisper model (optimized for size)
            global WHISPER_TYPE
            if WHISPER_TYPE == "faster":
                # Faster-whisper (much smaller size)
                self.model = WhisperModel("base", device="cpu", compute_type="int8")
                self.whisper_type = "faster"
            else:
                # OpenAI Whisper (fallback)
                self.model = whisper.load_model("base")
                self.whisper_type = "openai"

            # Audio buffer (in-memory only)
            self.audio_buffer = deque()
            self.is_recording = False
            self.last_sound_time = time.time()
            self.recording_start_time = None
            self.current_transcribed_text = ""

            # Threading
            self.stop_flag = threading.Event()
            self.chunk_counter = 0

            # API client for AI responses
            self.api_client = APIClient()

        except Exception as e:
            # Silent fail for exe - just emit error signal
            self.error_occurred = True
        

    
    def is_silent(self, audio_chunk):
        """Check if audio chunk is silent"""
        audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        return np.max(np.abs(audio_data)) < self.SILENCE_THRESHOLD
    
    def process_chunk_realtime(self, audio_data):
        """Process audio chunk for real-time transcription (optimized for speed)"""
        try:
            # Skip processing if audio data is too small (performance optimization)
            if len(audio_data) < 1024:
                return

            # Create in-memory WAV file (faster than disk I/O)
            wav_buffer = io.BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                wav_file.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)

            # Reset buffer position for reading
            wav_buffer.seek(0)

            # Use unique temp file with faster naming
            global TEMP_DIR
            temp_chunk_file = os.path.join(TEMP_DIR, f"chunk_{self.chunk_counter}.wav")
            self.chunk_counter += 1

            # Write temp file (minimal I/O)
            with open(temp_chunk_file, 'wb') as f:
                f.write(wav_buffer.getvalue())

            # Transcribe with Whisper (original working settings)
            if self.whisper_type == "faster":
                segments, info = self.model.transcribe(temp_chunk_file, language="en")
                text = " ".join([segment.text for segment in segments]).strip()
            else:
                result = self.model.transcribe(temp_chunk_file, language="en")
                text = result["text"].strip()

            # Immediate cleanup (non-blocking)
            try:
                os.remove(temp_chunk_file)
            except:
                pass

            if text and len(text.strip()) > 2:  # Only process meaningful text (optimized)
                # Update current transcribed text with smart comparison
                if len(text) > len(self.current_transcribed_text) or text != self.current_transcribed_text:
                    self.current_transcribed_text = text
                    # Emit signal to update GUI (non-blocking)
                    self.text_updated.emit(self.current_transcribed_text)

        except Exception as e:
            # Silent fail for exe
            pass

    def run(self):
        """Main thread execution for QThread"""
        self.record_audio()

    def record_audio(self):
        """Record audio from system/microphone"""
        try:
            stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK,
                # input_device_index=None  # Use default input device
            )

            chunk_buffer = []
            last_chunk_time = time.time()

            while not self.stop_flag.is_set():
                try:
                    audio_chunk = stream.read(self.CHUNK, exception_on_overflow=False)

                    if not self.is_silent(audio_chunk):
                        if not self.is_recording:
                            self.is_recording = True
                            self.audio_buffer.clear()
                            chunk_buffer.clear()
                            self.current_transcribed_text = ""  # Reset text
                            self.recording_start_time = time.time()
                            last_chunk_time = time.time()

                        self.audio_buffer.append(audio_chunk)
                        chunk_buffer.append(audio_chunk)
                        self.last_sound_time = time.time()

                        # Process chunk for real-time transcription every CHUNK_DURATION seconds
                        current_time = time.time()
                        if current_time - last_chunk_time >= self.CHUNK_DURATION and chunk_buffer:
                            # Use all audio from start for better accuracy
                            all_audio_data = b''.join(self.audio_buffer)
                            # Run transcription in separate thread to avoid blocking
                            threading.Thread(target=self.process_chunk_realtime, args=(all_audio_data,), daemon=True).start()
                            last_chunk_time = current_time

                    elif self.is_recording:
                        # Check if silence duration exceeded
                        if time.time() - self.last_sound_time > self.SILENCE_DURATION:
                            self.process_final_audio()
                            self.is_recording = False
                            self.current_transcribed_text = ""

                except Exception as e:
                    print(f"Error reading audio chunk: {e}")
                    continue

        except Exception as e:
            print(f"Error opening audio stream: {e}")
        finally:
            if 'stream' in locals():
                stream.stop_stream()
                stream.close()
    
    def process_final_audio(self):
        """Process complete recorded audio with Whisper for final result"""
        if not self.audio_buffer:
            return

        try:
            # Combine all audio chunks
            audio_data = b''.join(self.audio_buffer)

            # Create temporary file in temp directory
            global TEMP_DIR
            temp_final_file = os.path.join(TEMP_DIR, f"final_audio_{int(time.time())}.wav")

            # Save to temporary WAV file
            with wave.open(temp_final_file, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                wav_file.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)

            # Transcribe with Whisper for final result (original working settings)
            if self.whisper_type == "faster":
                segments, info = self.model.transcribe(temp_final_file, language="en")
                transcribed_text = " ".join([segment.text for segment in segments]).strip()
            else:
                result = self.model.transcribe(temp_final_file, language="en")
                transcribed_text = result["text"].strip()

            # Immediately clean up temporary file
            try:
                os.remove(temp_final_file)
            except:
                pass

            if transcribed_text:
                # Emit final text signal
                self.final_text.emit(transcribed_text)

                # Get AI response in separate thread
                threading.Thread(target=self.get_ai_response_async, args=(transcribed_text,), daemon=True).start()

        except Exception as e:
            # Silent fail for exe
            pass

    def get_ai_response_async(self, text):
        """Get AI response asynchronously with streaming chunks"""
        try:
            response = self.api_client.get_ai_response(text)
            model_name = self.api_client.get_last_used_model()

            # Start streaming response in chunks
            self.stream_response_chunks(response, model_name)

        except Exception as e:
            self.ai_response.emit(f"Error: {str(e)}|MODEL:Error")

    def stream_response_chunks(self, full_response, model_name):
        """Stream response in chunks to simulate real-time typing"""
        # Run streaming in separate thread to avoid blocking
        threading.Thread(target=self._stream_chunks_worker, args=(full_response, model_name), daemon=True).start()

    def _stream_chunks_worker(self, full_response, model_name):
        """Worker thread for streaming chunks (optimized for immediate visibility)"""
        # Split response into words for natural chunking
        words = full_response.split()
        chunk_size = 4  # Show 4 words at a time for faster display

        # Start with empty response
        current_text = ""

        # Emit model name first
        self.ai_response.emit(f"|MODEL:{model_name}")

        # Stream words in chunks with optimized timing for immediate response
        for i in range(0, len(words), chunk_size):
            chunk_words = words[i:i + chunk_size]
            current_text += " ".join(chunk_words) + " "

            # Emit current accumulated text
            self.ai_response.emit(f"{current_text.strip()}|MODEL:{model_name}")

            # Optimized streaming for immediate visibility
            if i < 8:  # First 2 chunks instantly for immediate feedback
                time.sleep(0.015)  # 15ms for immediate start
            elif i < 16:  # Next chunks very fast
                time.sleep(0.03)   # 30ms for quick progression
            else:
                time.sleep(0.06)   # 60ms for remaining chunks
    
    def start_transcription(self):
        """Start the real-time transcription thread"""
        if not self.isRunning():
            self.stop_flag.clear()
            self.start()

    def stop_transcription(self):
        """Stop the transcription"""
        self.stop_flag.set()
        if self.isRunning():
            self.wait()
        try:
            self.audio.terminate()
        except:
            pass

        # Clean up any remaining temp files
        self.cleanup_temp_files()

    def cleanup_temp_files(self):
        """Clean up temporary audio files"""
        try:
            global TEMP_DIR, AUDIO_BUFFERS
            AUDIO_BUFFERS.clear()

            if TEMP_DIR and os.path.exists(TEMP_DIR):
                # Clean all wav files in temp directory
                for file in os.listdir(TEMP_DIR):
                    if file.endswith('.wav'):
                        try:
                            os.remove(os.path.join(TEMP_DIR, file))
                        except:
                            pass
        except:
            pass

class LiveTranscriptionApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.transcriber = None

        # Initialize Firebase authentication
        self.firebase_manager = FirebaseManager()
        self.current_user_email = None
        self.remaining_time = 0
        self.user_data = None
        self.session_start_time = None

        # Show authentication dialog first
        print("🔄 Starting user authentication...")
        auth_result = self.authenticate_user()
        print(f"🔄 Authentication result: {auth_result}")

        if not auth_result:
            print("❌ Authentication failed, exiting...")
            sys.exit()
            return

        print("✅ Authentication successful, initializing UI...")
        self.init_ui()
        self.setup_keyboard_shortcuts()
        self.setup_capslock_functionality()
        self.apply_stealth_features()

        # Start time tracking and countdown
        self.setup_time_tracking()

        # Safe UI optimizations (non-breaking)
        self.setAttribute(Qt.WA_OpaquePaintEvent, True)
        self.setAttribute(Qt.WA_NoSystemBackground, False)

        print("✅ Main application initialized successfully")

    def authenticate_user(self):
        """Show authentication dialog and handle user login"""
        try:
            print("🔄 Starting authentication dialog...")

            auth_dialog = UserAuthDialog(self)
            auth_successful = False

            def on_user_authenticated(email, remaining_time, user_data):
                nonlocal auth_successful
                try:
                    print(f"✅ Signal received - User authenticated: {email}, time: {remaining_time}s")
                    self.current_user_email = email
                    self.remaining_time = remaining_time
                    self.user_data = user_data
                    self.session_start_time = datetime.now()
                    auth_successful = True
                    print("✅ Authentication data saved successfully")
                except Exception as e:
                    print(f"❌ Error saving auth data: {e}")
                    import traceback
                    traceback.print_exc()

            # Connect signal
            print("🔄 Connecting authentication signal...")
            auth_dialog.user_authenticated.connect(on_user_authenticated)
            print("✅ Signal connected successfully")

            # Show dialog
            print("🔄 Showing authentication dialog...")
            result = auth_dialog.exec_()

            print(f"🔄 Dialog result: {result}, auth_successful: {auth_successful}")
            print(f"🔄 Dialog.Accepted = {auth_dialog.Accepted}")

            if result == auth_dialog.Accepted and auth_successful:
                print("✅ Authentication completed successfully")
                return True
            else:
                print(f"❌ Authentication failed or cancelled - result: {result}, success: {auth_successful}")
                return False

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            import traceback
            traceback.print_exc()
            return False

    def setup_time_tracking(self):
        """Setup time tracking and title updates"""
        self.time_update_timer = QTimer()
        self.time_update_timer.timeout.connect(self.update_time_display)
        self.time_update_timer.start(1000)  # Update every second (safe frequency)

    def update_time_display(self):
        """Update window title with remaining time"""
        try:
            if self.session_start_time:
                # Calculate elapsed time
                elapsed = (datetime.now() - self.session_start_time).total_seconds()
                current_remaining = max(0, self.remaining_time - elapsed)

                # Convert to hours, minutes, seconds for better display
                hours = int(current_remaining // 3600)
                minutes = int((current_remaining % 3600) // 60)
                seconds = int(current_remaining % 60)

                # Update UI time display in H:M:S format
                if current_remaining > 0:
                    time_str = f"({hours:02d}:{minutes:02d}:{seconds:02d})"

                    # Update UI label if it exists
                    if hasattr(self, 'time_display_label'):
                        self.time_display_label.setText(time_str)

                    # Update window title (simplified)
                    self.setWindowTitle("InterV AI - Full Stack Developer")
                else:
                    # Time expired
                    if hasattr(self, 'time_display_label'):
                        self.time_display_label.setText("(00:00:00)")
                        self.time_display_label.setStyleSheet("color: #F44336; font-weight: bold; font-size: 12px;")

                    self.setWindowTitle("InterV AI - Full Stack Developer")

                    # Logout user
                    if not hasattr(self, '_logout_triggered'):
                        self._logout_triggered = True
                        self.logout_user()

        except Exception as e:
            pass

    def logout_user(self):
        """Logout user when time expires"""
        try:
            print("🔄 Logging out user - time expired...")

            # Stop time tracking timer
            if hasattr(self, 'time_update_timer'):
                self.time_update_timer.stop()

            # Update Firebase with logout time
            if hasattr(self, 'firebase_manager') and self.current_user_email:
                try:
                    user_ref = self.firebase_manager.db.collection('users').document(self.current_user_email)
                    user_ref.update({
                        'last_logout': firestore.SERVER_TIMESTAMP
                    })
                    print("✅ Updated logout time in Firebase")
                except Exception as e:
                    print(f"⚠️ Could not update logout time: {e}")

            # Show logout message
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox(self)
            msg.setWindowTitle("Session Expired")
            msg.setText("Your session time has expired.\nThe application will now close.")
            msg.setIcon(QMessageBox.Information)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setWindowFlags(msg.windowFlags() | Qt.WindowStaysOnTopHint)
            msg.exec_()

            # Close application
            print("✅ Closing application due to time expiry...")
            QApplication.quit()

        except Exception as e:
            print(f"❌ Error during logout: {e}")
            QApplication.quit()  # Force close on error

    def init_ui(self):
        """Initialize the user interface"""
        # Initial title - will be updated by time tracking
        self.setWindowTitle("InterV AI - Full Stack Developer Assistant")
        self.setGeometry(100, 100, 450, 650)

        # Set window flags for stealth (same as ANSARI_AI_V2)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)

        # Set window transparency
        self.setWindowOpacity(WINDOW_TRANSPARENCY)

        # Main widget and layout
        main_widget = QWidget()
        main_widget.setStyleSheet("background-color: white;")
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title bar with close button
        title_frame = QFrame()
        title_frame.setFixedHeight(35)
        title_frame.setStyleSheet("background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px;")
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 5, 10, 5)

        title_label = QLabel("InterV AI - Full Stack Developer 👨‍💻")
        title_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        title_label.mousePressEvent = self.title_mouse_press
        title_layout.addWidget(title_label)

        # Time display label
        self.time_display_label = QLabel("")
        self.time_display_label.setStyleSheet("color: #4CAF50; font-weight: bold; font-size: 12px;")
        title_layout.addWidget(self.time_display_label)

        close_btn = QPushButton("✕")
        close_btn.setFixedSize(25, 25)
        close_btn.setStyleSheet("background-color: #dc3545; color: white; border: none; border-radius: 12px; font-weight: bold;")
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)

        layout.addWidget(title_frame)

        # Question section
        question_label = QLabel("🎤 Interview Question:")
        question_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        layout.addWidget(question_label)

        self.question_text = QLabel()
        # Make it read-only display with proper text wrapping
        self.question_text.setWordWrap(True)
        self.question_text.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.question_text.setMinimumHeight(30)
        self.question_text.setMaximumHeight(100)

        self.question_text.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                color: #333;
                font-weight: normal;
            }
        """)
        self.question_text.setText("Speak your technical question and it will appear here...")
        layout.addWidget(self.question_text)

        # CapsLock input field (hidden by default)
        self.question_input = QTextEdit()
        self.question_input.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                color: #000000;
                font-size: 16px;
                font-family: 'Segoe UI', sans-serif;
                border: 4px solid #2196F3;
                border-radius: 12px;
                padding: 15px;
                selection-background-color: #2196F3;
                selection-color: white;
            }
            QTextEdit:focus {
                border: 4px solid #1976D2;
                background-color: #F8F9FA;
            }
        """)
        self.question_input.setPlaceholderText("🎤 Type your question here...")
        self.question_input.setMaximumHeight(120)
        self.question_input.setMinimumHeight(80)
        self.question_input.setVisible(False)  # Hidden by default
        self.is_input_mode = False
        layout.addWidget(self.question_input)

        # Response section
        self.response_label = QLabel("💡 Developer Answer:")
        self.response_label.setStyleSheet("color: #333; font-weight: bold; font-size: 12px;")
        layout.addWidget(self.response_label)

        self.response_text = QTextEdit()
        self.response_text.setFixedHeight(350)  # Increased from 320 to 350

        # Disable rich text - use plain text for proper line breaks
        self.response_text.setAcceptRichText(False)

        # Set larger monospace font for better visibility
        code_font = QFont("Consolas", 15)  # Increased from 13 to 15
        if not code_font.exactMatch():
            code_font = QFont("Courier New", 15)
        code_font.setFixedPitch(True)  # Ensure monospace
        self.response_text.setFont(code_font)

        self.response_text.setStyleSheet("""
            QTextEdit {
                background-color: #f0f8ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 12px;
                font-size: 15px;
                color: #333;
                line-height: 1.5;
                font-family: 'Consolas', 'Courier New', monospace;
            }
        """)
        self.response_text.setPlaceholderText("Full-stack developer answers will appear here with examples...")
        layout.addWidget(self.response_text)

        # Status label
        self.status_label = QLabel("🎤 Starting interview mode... Ready for technical questions!")
        self.status_label.setStyleSheet("color: #666; font-size: 10px; padding: 5px; background-color: #f0f0f0; border-radius: 4px;")
        layout.addWidget(self.status_label)

        # Instructions label
        instructions_label = QLabel("💡 Tips: Ask frontend, backend, database, or system design questions | Ctrl+Space (hide/show) | Ctrl+Arrows (move)")
        instructions_label.setStyleSheet("color: #888; font-size: 9px; padding: 4px;")
        instructions_label.setWordWrap(True)
        layout.addWidget(instructions_label)

        # Resize grip
        self.resize_grip = QSizeGrip(self)
        layout.addWidget(self.resize_grip, 0, Qt.AlignBottom | Qt.AlignRight)

        # Position window at bottom-right
        self.position_window_bottom_right()

        # Auto-start transcription immediately after UI is ready
        QTimer.singleShot(500, self.auto_start_transcription)

    def setup_keyboard_shortcuts(self):
        """Setup fast and smooth keyboard shortcuts"""
        # Enable focus and key events
        self.setFocusPolicy(Qt.StrongFocus)
        self.setFocus()

        # Very fast timer for responsive controls
        self.key_timer = QTimer()
        self.key_timer.timeout.connect(self.check_keys)
        self.key_timer.start(25)  # Check every 25ms for very fast movement

        # Key state tracking
        self.key_states = {
            'space': False,
            'up': False,
            'down': False,
            'left': False,
            'right': False,
            'caps_up': False,
            'caps_down': False
        }

        # Make window always on top for shortcuts to work
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)

        # Ensure window can receive key events
        self.setAttribute(Qt.WA_InputMethodEnabled, True)
        self.grabKeyboard()  # Capture all keyboard input

    def check_keys(self):
        """Check for key combinations with continuous movement support"""
        try:
            # Check if Ctrl is pressed
            ctrl_pressed = ctypes.windll.user32.GetAsyncKeyState(0x11) & 0x8000

            # Check if CapsLock is on
            caps_lock_on = ctypes.windll.user32.GetKeyState(0x14) & 0x0001

            # CapsLock + Arrow Keys: Scroll response text
            if caps_lock_on:
                up_now = ctypes.windll.user32.GetAsyncKeyState(0x26) & 0x8000
                down_now = ctypes.windll.user32.GetAsyncKeyState(0x28) & 0x8000

                if up_now and not self.key_states.get('caps_up', False):
                    self.key_states['caps_up'] = True
                    scrollbar = self.response_text.verticalScrollBar()
                    scrollbar.setValue(scrollbar.value() - 100)  # Scroll up
                elif not up_now:
                    self.key_states['caps_up'] = False

                if down_now and not self.key_states.get('caps_down', False):
                    self.key_states['caps_down'] = True
                    scrollbar = self.response_text.verticalScrollBar()
                    scrollbar.setValue(scrollbar.value() + 100)  # Scroll down
                elif not down_now:
                    self.key_states['caps_down'] = False

            elif ctrl_pressed:
                # Ctrl + Space - toggle window visibility (single press only)
                space_now = ctypes.windll.user32.GetAsyncKeyState(0x20) & 0x8000
                if space_now and not self.key_states['space']:
                    self.key_states['space'] = True
                    self.toggle_window_visibility()
                elif not space_now:
                    self.key_states['space'] = False

                # Arrow keys - continuous movement when held
                up_now = ctypes.windll.user32.GetAsyncKeyState(0x26) & 0x8000
                if up_now:
                    if not self.key_states['up']:
                        self.key_states['up'] = True
                    self.move_window("up")  # Move continuously while held
                else:
                    self.key_states['up'] = False

                down_now = ctypes.windll.user32.GetAsyncKeyState(0x28) & 0x8000
                if down_now:
                    if not self.key_states['down']:
                        self.key_states['down'] = True
                    self.move_window("down")  # Move continuously while held
                else:
                    self.key_states['down'] = False

                left_now = ctypes.windll.user32.GetAsyncKeyState(0x25) & 0x8000
                if left_now:
                    if not self.key_states['left']:
                        self.key_states['left'] = True
                    self.move_window("left")  # Move continuously while held
                else:
                    self.key_states['left'] = False

                right_now = ctypes.windll.user32.GetAsyncKeyState(0x27) & 0x8000
                if right_now:
                    if not self.key_states['right']:
                        self.key_states['right'] = True
                    self.move_window("right")  # Move continuously while held
                else:
                    self.key_states['right'] = False
            else:
                # Reset all key states when Ctrl is not pressed
                for key in self.key_states:
                    self.key_states[key] = False
        except:
            pass

    def setup_capslock_functionality(self):
        """Setup CapsLock functionality with keyboard listener"""
        try:
            # Initialize CapsLock state variables
            self.caps_lock_pressed = False
            self.shift_pressed = False
            self.alt_pressed = False
            self.ctrl_pressed = False
            self.pressed_keys = set()
            self.selection_mode_active = False
            self.first_caps_lock_use = True
            self.first_alt_use = True

            # Initialize keyboard signals
            self.keyboard_signals = KeyboardSignals()
            self.keyboard_signals.caps_lock_signal.connect(self.handle_caps_lock_press)
            self.keyboard_signals.screenshot_signal.connect(self.take_screenshot)
            self.keyboard_signals.submit_question_signal.connect(self._submit_question_from_signal)
            self.keyboard_signals.clear_text_signal.connect(self._clear_all_text)
            self.keyboard_signals.delete_char_signal.connect(self._delete_one_character)
            self.keyboard_signals.add_newline_signal.connect(self._add_new_line)
            self.keyboard_signals.add_char_signal.connect(self._add_character_to_input)
            self.keyboard_signals.show_selection_overlay_signal.connect(self.show_selection_overlay)
            self.keyboard_signals.hide_selection_overlay_signal.connect(self.hide_selection_overlay)
            self.keyboard_signals.resize_selection_signal.connect(self.resize_selection)
            self.keyboard_signals.move_selection_signal.connect(self.move_selection)

            # Initialize selection overlay
            self.selection_overlay = SelectionOverlay()
            self.selection_timeout_timer = QTimer()
            self.selection_timeout_timer.setSingleShot(True)
            self.selection_timeout_timer.timeout.connect(self.hide_selection_overlay)

            # Setup keyboard listener
            self.setup_keyboard_listener()

            print("✅ CapsLock functionality initialized successfully")

        except Exception as e:
            print(f"❌ Error setting up CapsLock functionality: {e}")

    def setup_keyboard_listener(self):
        """Setup global keyboard listener"""
        try:
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.keyboard_listener.start()
            print("✅ Keyboard listener started")
        except Exception as e:
            print(f"❌ Error starting keyboard listener: {e}")

    def on_key_press(self, key):
        """Handle key press events"""
        try:
            # Initialize pressed_keys set if it doesn't exist
            if not hasattr(self, 'pressed_keys'):
                self.pressed_keys = set()

            # Add key to pressed keys set
            self.pressed_keys.add(key)

            # Shift detection
            if key in [keyboard.Key.shift, keyboard.Key.shift_l, keyboard.Key.shift_r]:
                self.shift_pressed = True

            # Alt detection
            elif key in [keyboard.Key.alt, keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr]:
                self.alt_pressed = True
                # Show selection overlay immediately on ALT press
                if not self.selection_mode_active:
                    self.selection_mode_active = True
                    self.keyboard_signals.show_selection_overlay_signal.emit()

            # Ctrl detection
            elif key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_r or key == keyboard.Key.ctrl_l:
                self.ctrl_pressed = True

            # CapsLock detection
            elif key == keyboard.Key.caps_lock:
                self.keyboard_signals.caps_lock_signal.emit()

            # Handle Delete key to cancel selection overlay
            elif key == keyboard.Key.delete:
                if self.selection_mode_active:
                    self.selection_mode_active = False
                    if hasattr(self, 'selection_overlay'):
                        self.selection_overlay.hide()
                    if hasattr(self, 'selection_timeout_timer') and self.selection_timeout_timer.isActive():
                        self.selection_timeout_timer.stop()

            # Handle Escape key to cancel selection overlay
            elif key == keyboard.Key.esc:
                if self.selection_mode_active:
                    self.selection_mode_active = False
                    if hasattr(self, 'selection_overlay'):
                        self.selection_overlay.hide()
                    if hasattr(self, 'selection_timeout_timer') and self.selection_timeout_timer.isActive():
                        self.selection_timeout_timer.stop()

            # Handle input when CapsLock is ON and input mode is active
            elif self.is_input_mode and self.question_input.isVisible():
                # Handle Backspace for character deletion
                if key == keyboard.Key.backspace:
                    ctrl_is_pressed = (keyboard.Key.ctrl_l in self.pressed_keys or
                                     keyboard.Key.ctrl_r in self.pressed_keys or
                                     self.ctrl_pressed)

                    if ctrl_is_pressed:
                        # Ctrl+Backspace: Clear all text
                        self.keyboard_signals.clear_text_signal.emit()
                    else:
                        # Regular Backspace: Delete one character
                        self.keyboard_signals.delete_char_signal.emit()

                # Handle Enter key
                elif key == keyboard.Key.enter:
                    if self.shift_pressed:
                        # Shift+Enter: Add new line
                        self.keyboard_signals.add_newline_signal.emit()
                    else:
                        # Enter: Submit question
                        question_text = ""
                        try:
                            question_text = self.question_input.toPlainText().strip()
                        except:
                            pass
                        if question_text:
                            self.keyboard_signals.submit_question_signal.emit(question_text)

        except Exception as e:
            print(f"❌ Error in key press: {e}")

    def on_key_release(self, key):
        """Handle key release events"""
        try:
            # Remove key from pressed keys set
            if hasattr(self, 'pressed_keys') and key in self.pressed_keys:
                self.pressed_keys.remove(key)

            # Check if Alt key was released and handle screenshot capture
            if key in [keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr] and self.alt_pressed:
                if self.selection_mode_active and hasattr(self, 'selection_overlay') and self.selection_overlay.isVisible():
                    # Take screenshot of selected area
                    self.keyboard_signals.screenshot_signal.emit()

            # Update key state flags
            if key == keyboard.Key.caps_lock:
                self.caps_lock_pressed = False
            elif key in [keyboard.Key.shift, keyboard.Key.shift_l, keyboard.Key.shift_r]:
                self.shift_pressed = False
            elif key in [keyboard.Key.alt, keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr]:
                self.alt_pressed = False
            elif key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_r or key == keyboard.Key.ctrl_l:
                self.ctrl_pressed = False

        except Exception as e:
            print(f"❌ Error in key release: {e}")

    def get_caps_lock_state(self):
        """Get current Caps Lock state using Windows API"""
        try:
            import ctypes
            return ctypes.windll.user32.GetKeyState(0x14) & 0x0001 != 0
        except Exception as e:
            print(f"❌ Error getting Caps Lock state: {e}")
            return False

    def handle_caps_lock_press(self):
        """Handle Caps Lock press - toggle input mode"""
        # Use QTimer with single shot to handle caps lock with delay in main thread
        QTimer.singleShot(100, self._handle_caps_lock_delayed)

    def _handle_caps_lock_delayed(self):
        """Handle Caps Lock press with delay for state update"""
        try:
            # Check current Caps Lock state
            caps_lock_state = self.get_caps_lock_state()

            if caps_lock_state:  # Caps Lock is ON
                print("🔒 Caps Lock is ON - Showing question input")

                # Show and focus the main window
                self.show()
                self.raise_()
                self.activateWindow()

                # Show input field
                self.is_input_mode = True
                self.question_input.setVisible(True)
                self.question_input.clear()

                # Auto-populate with copied text from clipboard
                self._auto_populate_clipboard_text()

                self.question_input.setPlaceholderText("🎤 Type your question here and press Enter...")

                # Force focus
                self._force_input_focus()

                # Update status
                self.status_label.setText("🎤 INPUT MODE ACTIVE - Type your question and press Enter")

            else:  # Caps Lock is OFF
                print("🔒 Caps Lock is OFF - Hiding question input")

                # Hide input field
                self.is_input_mode = False
                self.question_input.setVisible(False)

                # Reset status
                self.status_label.setText("✅ Ready - Press Caps Lock to ask questions")

        except Exception as e:
            print(f"❌ Error handling caps lock press: {e}")

    def _auto_populate_clipboard_text(self):
        """Auto-populate input field with clipboard text when Caps Lock is pressed"""
        try:
            clipboard = QApplication.clipboard()
            clipboard_text = clipboard.text()

            if clipboard_text and clipboard_text.strip():
                self.question_input.setPlainText(clipboard_text.strip())
                print(f"📋 Auto-populated clipboard text: {clipboard_text[:50]}...")

                # Move cursor to end
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.End)
                self.question_input.setTextCursor(cursor)
            else:
                print("📋 No text in clipboard to auto-populate")

        except Exception as e:
            print(f"❌ Error auto-populating clipboard text: {e}")

    def _force_input_focus(self):
        """Force focus on input field"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                # Make sure window is active and on top
                self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)
                self.activateWindow()
                self.raise_()
                self.show()

                # Force focus on input field
                self.question_input.setFocus(Qt.OtherFocusReason)

                # Move cursor to end
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.End)
                self.question_input.setTextCursor(cursor)

        except Exception as e:
            print(f"❌ Error forcing input focus: {e}")

    def _clear_all_text(self):
        """Clear all text from input field (Ctrl+Backspace)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                current_text = self.question_input.toPlainText()
                self.question_input.clear()
                print(f"🗑️ Cleared all text (Ctrl+Backspace) - was: '{current_text[:50]}...'")

                # Move cursor to beginning
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.Start)
                self.question_input.setTextCursor(cursor)

        except Exception as e:
            print(f"❌ Error clearing all text: {e}")

    def _delete_one_character(self):
        """Delete one character from input field (Backspace)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                if not cursor.hasSelection():
                    cursor.deletePreviousChar()
                else:
                    cursor.removeSelectedText()
                print("⌫ Deleted one character")

        except Exception as e:
            print(f"❌ Error deleting character: {e}")

    def _add_new_line(self):
        """Add new line to input field (Shift+Enter)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                cursor.insertText("\n")
                print("↵ Added new line (Shift+Enter)")

        except Exception as e:
            print(f"❌ Error adding new line: {e}")

    def _add_character_to_input(self, char):
        """Add character to input field"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                cursor.insertText(char)

        except Exception as e:
            print(f"❌ Error adding character: {e}")

    def _submit_question_from_signal(self, question):
        """Handle question submission from signal (runs in main thread)"""
        try:
            print(f"✅ Processing question in main thread: {question}")

            # Clear and hide input immediately
            self.question_input.clear()
            self.question_input.setVisible(False)
            self.is_input_mode = False

            # Update status to show processing
            self.status_label.setText("🤔 Processing your question...")

            # Submit to AI (integrate with existing AI response system)
            self.submit_question_to_ai(question)

        except Exception as e:
            print(f"❌ Error submitting question: {e}")

    def show_selection_overlay(self):
        """Show selection overlay for screenshot"""
        try:
            if hasattr(self, 'selection_overlay'):
                self.selection_overlay.reset_selection_to_center()
                self.selection_overlay.show()
                self.selection_overlay.raise_()
                print("📸 Selection overlay shown")

                # Set timeout to auto-hide overlay
                self.selection_timeout_timer.start(10000)  # 10 seconds timeout

        except Exception as e:
            print(f"❌ Error showing selection overlay: {e}")

    def hide_selection_overlay(self):
        """Hide selection overlay"""
        try:
            if hasattr(self, 'selection_overlay'):
                self.selection_overlay.hide()
                self.selection_mode_active = False
                print("📸 Selection overlay hidden")

                # Stop timeout timer
                if hasattr(self, 'selection_timeout_timer') and self.selection_timeout_timer.isActive():
                    self.selection_timeout_timer.stop()

        except Exception as e:
            print(f"❌ Error hiding selection overlay: {e}")

    def resize_selection(self, direction, amount):
        """Resize selection overlay"""
        try:
            if hasattr(self, 'selection_overlay') and self.selection_overlay.isVisible():
                self.selection_overlay.resize_selection(direction, amount)

        except Exception as e:
            print(f"❌ Error resizing selection: {e}")

    def move_selection(self, direction, amount):
        """Move selection overlay"""
        try:
            if hasattr(self, 'selection_overlay') and self.selection_overlay.isVisible():
                self.selection_overlay.move_selection(direction, amount)

        except Exception as e:
            print(f"❌ Error moving selection: {e}")

    def take_screenshot(self):
        """Take screenshot of selected area"""
        try:
            if not hasattr(self, 'selection_overlay'):
                print("❌ No selection overlay available")
                return

            # Get selection bounds
            selection_bounds = self.selection_overlay.get_selection_bounds()
            if not selection_bounds:
                print("❌ No valid selection area found")
                return

            left, top, right, bottom = selection_bounds
            actual_width = right - left
            actual_height = bottom - top

            print(f"📸 Taking screenshot of area: ({left}, {top}) to ({right}, {bottom})")

            # Hide selection overlay before taking screenshot
            self.selection_overlay.hide()
            self.selection_mode_active = False

            # Hide main window temporarily for clean screenshot
            was_visible = self.isVisible()
            if was_visible:
                self.hide()
                time.sleep(0.3)  # Quick hide

            # Take screenshot of the selected area
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))

            # Show window again if it was visible
            if was_visible:
                self.show()
                self.raise_()
                self.activateWindow()

            # Store screenshot data for AI analysis (in memory only)
            self.current_screenshot_data = screenshot

            # Convert to QPixmap for display
            import io
            img_buffer = io.BytesIO()
            screenshot.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            pixmap = QPixmap()
            pixmap.loadFromData(img_buffer.getvalue())
            self.current_screenshot_pixmap = pixmap

            print(f"📸 Screenshot captured in memory - Size: {actual_width}x{actual_height}")

            # Display screenshot in response area
            self.display_screenshot_in_response()

        except Exception as e:
            print(f"❌ Error taking screenshot: {e}")

    def display_screenshot_in_response(self):
        """Display screenshot in response area"""
        try:
            if hasattr(self, 'current_screenshot_pixmap') and self.current_screenshot_pixmap:
                # Add screenshot info to response area
                timestamp = datetime.now().strftime("%H:%M:%S")
                screenshot_text = f"\n[{timestamp}] 📸 Screenshot captured and ready for AI analysis\n{'='*60}\n"

                # Append to response area
                current_text = self.response_text.toPlainText()
                self.response_text.setPlainText(current_text + screenshot_text)

                # Auto-scroll to bottom
                scrollbar = self.response_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

                print("📸 Screenshot displayed in response area")

        except Exception as e:
            print(f"❌ Error displaying screenshot: {e}")

    def submit_question_to_ai(self, question):
        """Submit question to AI response system"""
        try:
            # Create API client if not exists
            if not hasattr(self, 'api_client'):
                self.api_client = APIClient()

            # Add timestamp to response area
            timestamp = datetime.now().strftime("%H:%M:%S")
            question_text = f"\n[{timestamp}] 🤔 Question: {question}\n\n💡 AI Response:\n"

            # Append question to response area
            current_text = self.response_text.toPlainText()
            self.response_text.setPlainText(current_text + question_text)

            # Auto-scroll to bottom
            scrollbar = self.response_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Get AI response in separate thread
            def get_ai_response_async():
                try:
                    # Include screenshot in prompt if available
                    prompt = question
                    if hasattr(self, 'current_screenshot_data') and self.current_screenshot_data:
                        prompt = f"[Screenshot attached] {question}\n\nPlease analyze the screenshot and answer the question."

                    response = self.api_client.get_ai_response(prompt)
                    model_name = self.api_client.get_last_used_model()

                    # Emit response through signal for thread safety
                    QTimer.singleShot(0, lambda: self.handle_ai_response(response, model_name))

                except Exception as e:
                    error_msg = f"Error getting AI response: {str(e)}"
                    QTimer.singleShot(0, lambda: self.handle_ai_response(error_msg, "Error"))

            # Start AI response in background thread
            threading.Thread(target=get_ai_response_async, daemon=True).start()

        except Exception as e:
            print(f"❌ Error submitting question to AI: {e}")

    def handle_ai_response(self, response, model_name):
        """Handle AI response in main thread"""
        try:
            # Append AI response to response area
            current_text = self.response_text.toPlainText()
            formatted_response = f"{response}\n\n[Model: {model_name}]\n{'='*60}\n"
            self.response_text.setPlainText(current_text + formatted_response)

            # Auto-scroll to bottom
            scrollbar = self.response_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Update status
            self.status_label.setText("✅ Ready - Press Caps Lock to ask questions")

            # Clear screenshot data after use
            if hasattr(self, 'current_screenshot_data'):
                self.current_screenshot_data = None
            if hasattr(self, 'current_screenshot_pixmap'):
                self.current_screenshot_pixmap = None

            print(f"✅ AI response handled: {response[:50]}...")

        except Exception as e:
            print(f"❌ Error handling AI response: {e}")

    def position_window_bottom_right(self):
        """Position window at bottom-right corner"""
        screen = QApplication.desktop().screenGeometry()
        window_size = self.geometry()
        x = screen.width() - window_size.width() - 20
        y = screen.height() - window_size.height() - 60
        self.move(x, y)

    def apply_stealth_features(self):
        """Apply stealth features to hide from screen capture"""
        # Show the window first
        self.show()
        self.raise_()
        self.activateWindow()

        # Apply stealth after window is rendered
        QTimer.singleShot(500, self.hide_from_capture)
        QTimer.singleShot(1000, self.ensure_local_visibility)

    def hide_from_capture(self):
        """Enhanced hide window from screen capture (Windows 10/11 compatible)"""
        try:
            hwnd = int(self.winId())

            # PRIMARY METHOD: Set window to be excluded from capture (Windows 10+)
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result1 = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            # SECONDARY METHOD: Only apply if LOCAL_VISIBILITY_MODE is disabled
            if result1 and not LOCAL_VISIBILITY_MODE:
                # Make window non-enumerable (harder to detect) - only if local visibility not needed
                GWL_EXSTYLE = -20
                WS_EX_NOACTIVATE = 0x08000000
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_NOACTIVATE | WS_EX_TOOLWINDOW

                result2 = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                # Set window to bottom of Z-order for additional stealth
                HWND_BOTTOM = 1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_NOACTIVATE = 0x0010

                ctypes.windll.user32.SetWindowPos(
                    hwnd, HWND_BOTTOM, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE
                )

            # Store hwnd for later use
            self.hwnd = hwnd

            # Hide from taskbar if configured
            if ENABLE_TASKBAR_HIDING:
                self.hide_from_taskbar()

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error hiding from capture: {e}")

    def hide_from_taskbar(self):
        """Hide window from taskbar"""
        try:
            if ENABLE_TASKBAR_HIDING:
                hwnd = int(self.winId())

                # Hide from taskbar and Alt+Tab
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW
                ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if DEBUG_STEALTH_FEATURES:
                    print("✅ Window hidden from taskbar")
        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error hiding from taskbar: {e}")

    def ensure_local_visibility(self):
        """Ensure window remains visible locally"""
        try:
            self.show()
            self.raise_()
            self.setWindowOpacity(WINDOW_TRANSPARENCY)

            if hasattr(self, 'hwnd'):
                HWND_TOPMOST = -1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_SHOWWINDOW = 0x0040

                ctypes.windll.user32.SetWindowPos(
                    self.hwnd, HWND_TOPMOST, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_SHOWWINDOW
                )
        except Exception as e:
            pass

    def auto_start_transcription(self):
        """Automatically start transcription when application loads"""
        try:
            self.transcriber = RealTimeTranscriber()

            # Connect signals
            self.transcriber.text_updated.connect(self.update_question_text)
            self.transcriber.final_text.connect(self.update_final_text)
            self.transcriber.ai_response.connect(self.update_response_text)

            # Start transcription
            self.transcriber.start_transcription()

            # Update UI - Always listening
            self.status_label.setText("🎤 Always Listening...")
            self.question_text.setText("Speak your technical question and it will appear here...")  # setText for QLabel
            self.response_text.clear()

        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")

    def update_question_text(self, text):
        """Update real-time question text"""
        self.question_text.setText(text)  # Changed from setPlainText to setText for QLabel
        self.status_label.setText("🗣️ Listening to your question...")

    def update_final_text(self, text):
        """Update final transcribed text"""
        self.question_text.setText(text)  # Changed from setPlainText to setText for QLabel
        self.status_label.setText("🤖 Preparing developer answer...")

    def update_response_text(self, response_with_model):
        """Update AI response text with streaming support"""
        # Extract response and model name
        if "|MODEL:" in response_with_model:
            response, model_info = response_with_model.split("|MODEL:", 1)
            model_name = model_info.strip()
        else:
            response = response_with_model
            model_name = "Unknown"

        # Update response label with model name (only once)
        if not hasattr(self, 'current_model') or self.current_model != model_name:
            self.current_model = model_name
            self.response_label.setText(f"💡 Developer Answer (via {model_name}):")

            # Clear response text for new streaming
            if response.strip():  # Only clear if there's actual content
                self.response_text.clear()

        # Update response text (streaming) with proper formatting
        if response.strip():  # Only update if there's content
            # Clean up response and ensure proper formatting
            cleaned_response = self.clean_response_format(response)

            # Use setPlainText to preserve line breaks and formatting
            self.response_text.setPlainText(cleaned_response)

            # Auto-scroll to bottom to show latest text
            cursor = self.response_text.textCursor()
            cursor.movePosition(cursor.End)
            self.response_text.setTextCursor(cursor)

            # Update status to show streaming
            self.status_label.setText(f"📝 Streaming answer from {model_name}...")

        # Check if this is the final chunk (no more updates expected)
        QTimer.singleShot(200, self.check_streaming_complete)

    def check_streaming_complete(self):
        """Check if streaming is complete and update status"""
        if hasattr(self, 'current_model'):
            self.status_label.setText(f"✅ Answer ready! Ask another technical question...")

    def toggle_window_visibility(self):
        """Fast toggle window visibility"""
        if self.isVisible():
            self.hide()
        else:
            self.show()
            self.raise_()
            self.activateWindow()

    def hide_window(self):
        """Hide the window"""
        self.hide()

    def show_window(self):
        """Show the window"""
        self.show()
        self.raise_()
        self.activateWindow()
        
    def move_window(self, direction):
        """Move window continuously when key is held"""
        current_pos = self.pos()
        move_distance = 35  # Very fast movement distance

        # Calculate new position
        if direction == "up":
            new_x, new_y = current_pos.x(), current_pos.y() - move_distance
        elif direction == "down":
            new_x, new_y = current_pos.x(), current_pos.y() + move_distance
        elif direction == "left":
            new_x, new_y = current_pos.x() - move_distance, current_pos.y()
        elif direction == "right":
            new_x, new_y = current_pos.x() + move_distance, current_pos.y()
        else:
            return

        # Quick bounds check
        screen = QApplication.desktop().screenGeometry()
        window_size = self.size()

        # Clamp to screen bounds
        x = max(0, min(new_x, screen.width() - window_size.width()))
        y = max(0, min(new_y, screen.height() - window_size.height()))

        # Move window
        self.move(x, y)

    def title_mouse_press(self, event):
        """Handle title bar click to focus window for keyboard shortcuts"""
        if event.button() == Qt.LeftButton:
            self.setFocus()  # Give focus to window for keyboard shortcuts
            self.drag_start_position = event.globalPos()

    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.LeftButton:
            self.setFocus()  # Give focus to window
            self.drag_start_position = event.globalPos()

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_start_position'):
            self.move(self.pos() + event.globalPos() - self.drag_start_position)
            self.drag_start_position = event.globalPos()

    def clean_response_format(self, response):
        """Clean and format AI response for proper display"""
        # If response contains code mixed with definition, try to separate it
        lines = response.split('\n')
        cleaned_lines = []

        for line in lines:
            # Check if line contains both definition and code mixed together
            if ('function' in line.lower() or 'class' in line.lower() or
                'def ' in line.lower() or 'const ' in line.lower()) and len(line) > 80:

                # Try to split definition and code
                if 'Example:' in line:
                    parts = line.split('Example:', 1)
                    cleaned_lines.append(parts[0].strip())
                    cleaned_lines.append('')
                    cleaned_lines.append('Example:')

                    # Process code part
                    code_part = parts[1].strip()
                    # Simple code formatting - split on common patterns
                    code_part = code_part.replace('{ ', '{\n    ')
                    code_part = code_part.replace('; ', ';\n    ')
                    code_part = code_part.replace('} ', '}\n')
                    cleaned_lines.append(code_part)
                else:
                    cleaned_lines.append(line)
            else:
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def closeEvent(self, event):
        """Handle window close event"""
        try:
            # Release keyboard grab
            self.releaseKeyboard()

            # Stop keyboard listener
            if hasattr(self, 'keyboard_listener'):
                try:
                    self.keyboard_listener.stop()
                    print("✅ Keyboard listener stopped")
                except:
                    pass

            # Hide selection overlay
            if hasattr(self, 'selection_overlay'):
                try:
                    self.selection_overlay.hide()
                except:
                    pass

            if self.transcriber:
                self.transcriber.stop_transcription()
            cleanup_temp_directory()
            event.accept()
        except:
            event.accept()

def main():
    """Main function optimized for exe"""
    try:
        print("🚀 Starting InterV AI...")

        # Set working directory for exe
        if getattr(sys, 'frozen', False):
            exe_dir = os.path.dirname(sys.executable)
            os.chdir(exe_dir)
            print(f"📁 Working directory: {exe_dir}")

        # Create QApplication
        print("🔄 Creating QApplication...")
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        app.setApplicationName("InterV AI - Full Stack Developer")
        app.setApplicationVersion("2.0")

        # Create and show main window
        print("🔄 Creating main window...")
        window = LiveTranscriptionApp()

        if window:
            print("✅ Main window created successfully")
            window.show()
            print("✅ Application started successfully")
            sys.exit(app.exec_())
        else:
            print("❌ Failed to create main window")
            sys.exit(1)

    except Exception as e:
        print(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()

        # Show error dialog for debugging
        if not getattr(sys, 'frozen', False):
            try:
                from PyQt5.QtWidgets import QMessageBox
                app = QApplication.instance()
                if not app:
                    app = QApplication(sys.argv)

                msg = QMessageBox()
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("InterV AI Error")
                msg.setText(f"Application failed to start:\n\n{str(e)}")
                msg.exec_()
            except:
                pass

        sys.exit(1)

if __name__ == "__main__":
    main()

# Build command for creating LIGHTWEIGHT exe:
# pip install pyinstaller PyQt5 pyaudio faster-whisper numpy requests firebase-admin
# python -m PyInstaller --onefile --noconsole --hidden-import=faster_whisper --hidden-import=PyQt5.QtCore --hidden-import=PyQt5.QtGui --hidden-import=PyQt5.QtWidgets --hidden-import=pyaudio --hidden-import=numpy --hidden-import=firebase_admin --exclude-module=torch --exclude-module=tensorflow --exclude-module=matplotlib --exclude-module=scipy --exclude-module=pandas --optimize=2 --strip --name=InterV_AI_Optimized live.py 