# API Configuration
# Store your API keys here

# Google Gemini API Key
GOOGLE_GEMINI_API_KEY = 'AIzaSyDXQnE0sY40tZUf6TBUb4y-4t7mUVHvSYo'

# API Keys with Fallback System (Priority Order: Gemini > Mistral > OpenRouter > OpenAI)
API_KEYS = {
    'gemini': 'AIzaSyDXQnE0sY40tZUf6TBUb4y-4t7mUVHvSYo',
    'mistral': 'p7KFyMJyUNKqwNDUX4kdKwEEpc5eTAA1',
    'openrouter': '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',
    'openai': 'sk-1234567890abcdef'  # Add your actual OpenAI API key
}

# Fallback API Priority Order
API_FALLBACK_ORDER = ['gemini', 'mistral', 'openrouter', 'openai']

# API Endpoints
API_ENDPOINTS = {
    'gemini': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent',
    'mistral': 'https://api.mistral.ai/v1/chat/completions',
    'openrouter': 'https://openrouter.ai/api/v1/chat/completions',
    'openai': 'https://api.openai.com/v1/chat/completions'
}

# Configuration settings ..
DEBUG = False
CACHE_ENABLED = True
CACHE_TTL = 300  # 5 minutes 
