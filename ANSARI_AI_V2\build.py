#!/usr/bin/env python3
"""
Interview Assistant - Optimized Build Script
Creates a single standalone executable file
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def print_status(message, status="info"):
    """Print colored status messages"""
    colors = {
        "info": "🔵",
        "success": "✅", 
        "warning": "⚠️",
        "error": "❌",
        "building": "🔨"
    }
    print(f"{colors.get(status, '🔵')} {message}")

def cleanup_directories():
    """Clean up build directories"""
    print_status("Cleaning up previous builds...", "building")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print_status(f"Removed {dir_name}", "success")
            except Exception as e:
                print_status(f"Could not remove {dir_name}: {e}", "warning")
    
    # Clean .pyc files
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass

def check_dependencies():
    """Check if all required dependencies are installed"""
    print_status("Checking dependencies...", "info")
    
    required_packages = [
        "PyQt5",
        "pyaudio", 
        "SpeechRecognition",
        "requests",
        "pynput",
        "pyinstaller"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "PyQt5":
                import PyQt5
            elif package == "pyaudio":
                import pyaudio
            elif package == "SpeechRecognition":
                import speech_recognition
            elif package == "requests":
                import requests
            elif package == "pynput":
                import pynput
            elif package == "pyinstaller":
                import PyInstaller
            print_status(f"{package} - OK", "success")
        except ImportError:
            missing_packages.append(package)
            print_status(f"{package} - MISSING", "error")
    
    if missing_packages:
        print_status("Installing missing packages...", "building")
        for package in missing_packages:
            subprocess.run([sys.executable, "-m", "pip", "install", package])
    
    return len(missing_packages) == 0

def create_spec_file():
    """Create optimized PyInstaller spec file"""
    print_status("Creating optimized spec file...", "building")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

block_cipher = None

# Get the current directory
current_dir = os.path.dirname(os.path.abspath('main.py'))

a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('stealth_config.py', '.'),
        ('audio_device_manager.py', '.'),
        ('audio_handler.py', '.'),
        ('api_client.py', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        'pyaudio',
        'speech_recognition',
        'requests',
        'pynput',
        'pynput.keyboard',
        'pynput.mouse',
        'ctypes',
        'threading',
        'json',
        'time',
        'hashlib',
        'io',
        'wave',
        'struct',
        'os',
        'sys',
        'gc',
        'urllib3',
        'urllib3.util.retry'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'tkinter',
        'unittest',
        'test',
        'tests',
        'torch',
        'tensorflow',
        'keras',
        'sklearn',
        'cv2',
        'opencv',
        'jupyter',
        'notebook',
        'IPython',
        'distutils',
        'setuptools'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='InterviewAssistant',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open("interview_assistant.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print_status("Spec file created successfully", "success")

def build_executable():
    """Build the executable using PyInstaller"""
    print_status("Building standalone executable...", "building")
    print_status("This may take 3-5 minutes...", "info")
    
    try:
        # Run PyInstaller with the spec file
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "interview_assistant.spec"
        ]
        
        print_status(f"Running: {' '.join(cmd)}", "info")
        
        # Run with timeout
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=os.getcwd()
        )
        
        # Monitor progress
        start_time = time.time()
        timeout = 600  # 10 minutes timeout
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # Show important progress messages
                if any(keyword in output.lower() for keyword in ['building', 'analyzing', 'collecting', 'warning', 'error']):
                    print(f"  {output.strip()}")
            
            # Check timeout
            if time.time() - start_time > timeout:
                process.terminate()
                raise TimeoutError("Build process timed out")
        
        return_code = process.poll()
        
        if return_code == 0:
            print_status("Build completed successfully!", "success")
            return True
        else:
            print_status(f"Build failed with return code: {return_code}", "error")
            return False
            
    except Exception as e:
        print_status(f"Build error: {e}", "error")
        return False

def verify_build():
    """Verify that the executable was created successfully"""
    exe_path = Path("dist/InterviewAssistant.exe")
    
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print_status(f"Executable created: {exe_path}", "success")
        print_status(f"File size: {size_mb:.1f} MB", "info")
        return True
    else:
        print_status("Executable not found in dist folder", "error")
        return False

def main():
    """Main build process"""
    print("🚀 Interview Assistant - Optimized Build Process")
    print("=" * 55)
    
    # Step 1: Cleanup
    cleanup_directories()
    
    # Step 2: Check dependencies
    if not check_dependencies():
        print_status("Please install missing dependencies and try again", "error")
        return False
    
    # Step 3: Create spec file
    create_spec_file()
    
    # Step 4: Build executable
    if not build_executable():
        print_status("Build failed! Check error messages above.", "error")
        return False
    
    # Step 5: Verify build
    if not verify_build():
        print_status("Build verification failed!", "error")
        return False
    
    print("\n" + "=" * 55)
    print_status("BUILD SUCCESSFUL! 🎉", "success")
    print_status("Executable location: dist/InterviewAssistant.exe", "info")
    print_status("You can now run the standalone executable", "success")
    print("=" * 55)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print_status("\nBuild cancelled by user", "warning")
        sys.exit(1)
    except Exception as e:
        print_status(f"Unexpected error: {e}", "error")
        sys.exit(1)
