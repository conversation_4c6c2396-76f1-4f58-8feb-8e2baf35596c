#!/usr/bin/env python3
"""
Simple Build Script for Interview Assistant
Direct PyInstaller approach without complex spec file
"""

import os
import sys
import shutil
import subprocess

def cleanup():
    """Clean up previous builds"""
    print("🧹 Cleaning up previous builds...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Removed {dir_name}")
            except Exception as e:
                print(f"⚠️ Could not remove {dir_name}: {e}")

def build_simple():
    """Build using simple PyInstaller command"""
    print("🔨 Building Interview Assistant...")
    print("This will create a single executable file")
    
    # Simple PyInstaller command
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # Single file
        "--windowed",                   # No console window
        "--name=InterviewAssistant",    # Output name
        "--add-data=config.py;.",       # Include config
        "--add-data=stealth_config.py;.", # Include stealth config
        "--add-data=audio_device_manager.py;.", # Include audio manager
        "--add-data=audio_handler.py;.", # Include audio handler
        "--add-data=api_client.py;.",   # Include API client
        "--exclude-module=torch",       # Exclude heavy modules
        "--exclude-module=tensorflow",
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "--exclude-module=matplotlib",
        "--exclude-module=PIL",
        "--exclude-module=cv2",
        "--clean",                      # Clean cache
        "main.py"                       # Main file
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False

def verify_build():
    """Verify the build was successful"""
    exe_path = "dist/InterviewAssistant.exe"
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"✅ Executable created: {exe_path}")
        print(f"📦 Size: {size:.1f} MB")
        return True
    else:
        print("❌ Executable not found!")
        return False

def main():
    """Main build process"""
    print("🚀 Simple Interview Assistant Build")
    print("=" * 40)
    
    # Step 1: Cleanup
    cleanup()
    
    # Step 2: Build
    if not build_simple():
        print("❌ Build failed!")
        return False
    
    # Step 3: Verify
    if not verify_build():
        print("❌ Build verification failed!")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 BUILD SUCCESSFUL!")
    print("📁 Location: dist/InterviewAssistant.exe")
    print("🚀 You can now run the standalone executable")
    print("=" * 40)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Build cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
