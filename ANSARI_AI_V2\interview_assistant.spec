# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files and submodules
datas = []
hiddenimports = []

# Core PyQt5 dependencies (compatible with all Python versions)
hiddenimports.extend([
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'PyQt5.QtNetwork'
])

# Audio and speech dependencies
hiddenimports.extend([
    'pyaudio',
    'speech_recognition',
    'speech_recognition.microphone',
    'pynput',
    'pynput.keyboard',
    'pynput.mouse',
    'requests',
    'urllib3',
    'json',
    'time',
    'threading'
])

# Windows-specific dependencies
if sys.platform == 'win32':
    hiddenimports.extend([
        'pycaw',
        'comtypes',
        'psutil',
        'win32api',
        'win32con',
        'win32gui'
    ])

# Application modules
hiddenimports.extend([
    'audio_handler',
    'api_client',
    'ui_manager',
    'audio_device_manager',
    'config',
    'config1',
    'stealth_config'
])

# Python version compatibility
import sys
if sys.version_info >= (3, 8):
    hiddenimports.extend([
        'importlib.metadata',
        'importlib_metadata'
    ])

# Additional compatibility imports
hiddenimports.extend([
    'pkg_resources',
    'setuptools',
    'distutils'
])

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy.random._examples',
        'tkinter.test',
        'test',
        'unittest',
        'pydoc_data',
        'xml'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='InterviewAssistant',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,                    # Strip debug symbols for smaller size
    upx=True,                      # Compress executable
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,                 # Windowed application (no console)
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,                     # Add icon file path here if you have one
    version_file=None,             # Add version file here if you have one
    onefile=True,                  # Create single executable file
    optimize=2                     # Optimize bytecode for performance
)

# Optional: Create a directory distribution instead of single file
# Uncomment the following lines if you prefer a directory distribution
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='InterviewAssistant'
# )
