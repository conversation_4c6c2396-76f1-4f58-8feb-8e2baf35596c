import firebase_admin
from firebase_admin import credentials, firestore
import json
import os
from datetime import datetime, timedelta
import time

class FirebaseManager:
    def __init__(self):
        self.db = None
        self.user_listener = None
        self.current_user_email = None
        self.user_data_callback = None
        self.initialize_firebase()
    
    def initialize_firebase(self):
        """Initialize Firebase with service account credentials"""
        try:
            # Firebase service account configuration
            firebase_config = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            
            # Initialize Firebase Admin SDK
            if not firebase_admin._apps:
                cred = credentials.Certificate(firebase_config)
                firebase_admin.initialize_app(cred)
            
            # Initialize Firestore
            self.db = firestore.client()
            print("✅ Firebase initialized successfully")
            
        except Exception as e:
            print(f"❌ Firebase initialization error: {e}")
            self.db = None
    
    def validate_user(self, email):
        """
        Validate user and return user data with remaining time
        Returns: (is_valid, remaining_time_seconds, user_data)
        """
        try:
            if not self.db:
                return False, 0, None
            
            # Get user document from Firestore
            user_ref = self.db.collection('users').document(email)
            user_doc = user_ref.get()
            
            if not user_doc.exists:
                print(f"❌ User {email} not found in database")
                return False, 0, None
            
            user_data = user_doc.to_dict()
            
            # Check if user has allocated time
            allocated_minutes = user_data.get('allocated_time_minutes', 0)
            used_minutes = user_data.get('used_time_minutes', 0)
            
            remaining_minutes = allocated_minutes - used_minutes
            remaining_seconds = remaining_minutes * 60
            
            if remaining_seconds <= 0:
                print(f"❌ User {email} has no remaining time")
                return False, 0, user_data
            
            print(f"✅ User {email} validated. Remaining time: {remaining_minutes} minutes")
            return True, remaining_seconds, user_data

        except Exception as e:
            print(f"❌ Error validating user {email}: {e}")
            return False, 0, None

    def add_test_user(self, email, allocated_minutes=60):
        """Add a test user to Firebase for testing purposes"""
        try:
            if not self.db:
                print("❌ Firebase not initialized")
                return False

            user_data = {
                'email': email,
                'allocated_time_minutes': allocated_minutes,
                'used_time_minutes': 0,
                'created_at': datetime.now(),
                'last_login': None,
                'status': 'active'
            }

            # Add user to Firestore
            user_ref = self.db.collection('users').document(email)
            user_ref.set(user_data)

            print(f"✅ Test user {email} added with {allocated_minutes} minutes")
            return True

        except Exception as e:
            print(f"❌ Error adding test user: {e}")
            return False
    
    def update_user_time(self, email, used_seconds):
        """
        Update user's used time in Firebase
        """
        try:
            if not self.db:
                return False
            
            user_ref = self.db.collection('users').document(email)
            user_doc = user_ref.get()
            
            if user_doc.exists:
                current_used_minutes = user_doc.to_dict().get('used_time_minutes', 0)
                additional_minutes = used_seconds / 60
                new_used_minutes = current_used_minutes + additional_minutes
                
                user_ref.update({
                    'used_time_minutes': new_used_minutes,
                    'last_used': datetime.now()
                })
                
                print(f"✅ Updated time for {email}: +{additional_minutes:.2f} minutes")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error updating user time: {e}")
            return False
    
    def start_user_listener(self, email, callback):
        """
        Start real-time listener for user data updates
        """
        try:
            if not self.db:
                print("❌ Firebase not initialized")
                return False

            # Stop existing listener if any
            self.stop_user_listener()

            self.current_user_email = email
            self.user_data_callback = callback

            # Create real-time listener
            user_ref = self.db.collection('users').document(email)
            self.user_listener = user_ref.on_snapshot(self._on_user_data_changed)

            print(f"✅ Started real-time listener for user: {email}")
            return True

        except Exception as e:
            print(f"❌ Error starting user listener: {e}")
            return False

    def _on_user_data_changed(self, doc_snapshot, changes, read_time):
        """
        Handle real-time user data changes
        """
        try:
            for doc in doc_snapshot:
                if doc.exists:
                    user_data = doc.to_dict()
                    print(f"🔄 User data updated for {self.current_user_email}")

                    # Calculate remaining time
                    allocated_minutes = user_data.get('allocated_time_minutes', 0)
                    used_minutes = user_data.get('used_time_minutes', 0)
                    remaining_minutes = allocated_minutes - used_minutes
                    remaining_seconds = remaining_minutes * 60

                    # Call the callback with updated data
                    if self.user_data_callback:
                        self.user_data_callback(remaining_seconds, user_data)

        except Exception as e:
            print(f"❌ Error handling user data change: {e}")

    def stop_user_listener(self):
        """
        Stop the real-time listener
        """
        try:
            if self.user_listener:
                self.user_listener.unsubscribe()
                self.user_listener = None
                print(f"✅ Stopped listener for user: {self.current_user_email}")

        except Exception as e:
            print(f"❌ Error stopping user listener: {e}")

    def format_time(self, seconds):
        """
        Format seconds into H:M:S format
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
