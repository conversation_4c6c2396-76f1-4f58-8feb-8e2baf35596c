# Stealth Configuration for Interview Assistant
# This file controls the stealth features of the application

# Window transparency (0.0 = fully transparent, 1.0 = fully opaque)
WINDOW_TRANSPARENCY = 0.90  # Clearly visible to you locally

# Enable/disable screen capture hiding
ENABLE_SCREEN_CAPTURE_HIDING = True  # ENABLED - Hidden from screen capture ONLY

# Enable/disable taskbar hiding
ENABLE_TASKBAR_HIDING = False  # DISABLED - Keep visible in taskbar for you

# Enable/disable click-through mode by default
ENABLE_CLICK_THROUGH_DEFAULT = False  # Keep disabled for easier interaction

# Window positioning preferences
WINDOW_POSITION = "bottom-right"  # Options: "bottom-right", "top-right", "bottom-left", "top-left"

# Advanced stealth settings
ADVANCED_STEALTH_MODE = False  # DISABLED - Keep window visible to you
AUTO_HIDE_ON_SCREEN_SHARE = False  # DISABLED - Don't hide window from you
MINIMIZE_TO_SYSTEM_TRAY = False  # Keep disabled for now

# Hotkey settings
HOTKEY_TOGGLE_VISIBILITY = "ctrl+space"
HOTKEY_TOGGLE_LISTENING = "caps_lock"
HOTKEY_MOVE_WINDOW = "ctrl+arrow_keys"

# UI Settings
COMPACT_MODE = True  # Use smaller, more discrete UI
SHOW_TOOLTIPS = True  # Show helpful tooltips
DARK_MODE = False  # Use dark theme (not implemented yet)

# Debug settings
DEBUG_STEALTH_FEATURES = False  # DISABLED - No debug output during interviews
VERBOSE_LOGGING = False  # DISABLED - No verbose logging during interviews

# Enhanced stealth settings for maximum interview safety
EMERGENCY_HIDE_HOTKEY = "ctrl+shift+h"  # Emergency hide hotkey
PANIC_MODE_ENABLED = True  # Enable panic mode for emergency hiding
STEALTH_STARTUP_DELAY = 2.0  # Delay before applying stealth features (seconds)
MULTI_MONITOR_STEALTH = True  # Enhanced stealth for multi-monitor setups
PROCESS_HIDING_ENABLED = True  # Hide from process enumeration
WINDOW_TITLE_OBFUSCATION = True  # Obfuscate window title
MEMORY_FOOTPRINT_REDUCTION = True  # Reduce memory footprint for stealth

# Screen sharing detection enhancement
ENHANCED_SCREEN_SHARE_DETECTION = True  # Enhanced detection algorithms
SCREEN_SHARE_CHECK_INTERVAL = 3.0  # Check every 3 seconds (reduced for safety)
PREEMPTIVE_HIDING = True  # Hide before screen sharing fully starts

# Interview safety mode
INTERVIEW_SAFETY_MODE = True  # Enable maximum safety during interviews
SILENT_ERROR_HANDLING = True  # All errors handled silently
NO_POPUP_DIALOGS = True  # Disable all popup dialogs
BACKGROUND_ONLY_MODE = False  # Keep window visible to user but hidden from capture
LOCAL_VISIBILITY_MODE = True  # Ensure window remains visible to local user

print("🎯 BALANCED STEALTH MODE - Visible to You, Hidden from Screen Share!")
print(f"   • Screen capture hiding: {'ON' if ENABLE_SCREEN_CAPTURE_HIDING else 'OFF'}")
print(f"   • Taskbar visibility: {'HIDDEN' if ENABLE_TASKBAR_HIDING else 'VISIBLE'}")
print(f"   • Window transparency: {WINDOW_TRANSPARENCY} (visible to you)")
print(f"   • Local visibility mode: {'ON' if LOCAL_VISIBILITY_MODE else 'OFF'}")
print(f"   • Interview safety mode: {'ON' if INTERVIEW_SAFETY_MODE else 'OFF'}")
print("✅ You can see the window clearly")
print("🥷 Interviewer cannot see it in screen share!")
print("🔒 Perfect balance: Local visibility + Screen share stealth")
