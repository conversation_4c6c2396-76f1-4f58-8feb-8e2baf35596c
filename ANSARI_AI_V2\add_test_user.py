#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add test users to Firebase for testing authentication
"""

from firebase_config import FirebaseManager

def main():
    print("🔧 Adding test users to Firebase...")
    
    firebase_manager = FirebaseManager()
    
    # Test users to add
    test_users = [
        {"email": "<EMAIL>", "minutes": 60},
        {"email": "<EMAIL>", "minutes": 30},
        {"email": "<EMAIL>", "minutes": 120},
        {"email": "<EMAIL>", "minutes": 240}
    ]
    
    for user in test_users:
        success = firebase_manager.add_test_user(user["email"], user["minutes"])
        if success:
            print(f"✅ Added: {user['email']} with {user['minutes']} minutes")
        else:
            print(f"❌ Failed to add: {user['email']}")
    
    print("\n🎯 Test users added! You can now test authentication with:")
    for user in test_users:
        print(f"   • {user['email']}")

if __name__ == "__main__":
    main()
