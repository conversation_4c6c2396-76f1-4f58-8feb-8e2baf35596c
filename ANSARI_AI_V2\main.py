import sys
import threading
import ctypes
import gc
import time
from pynput import keyboard
from datetime import datetime

# PyQt5 imports
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit, QFrame,
                            QMessageBox, QComboBox, QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QFont

from audio_handler import AudioHandler
from api_client import APIClient

# Optional imports for user authentication
try:
    from firebase_config import FirebaseManager
    from user_auth_dialog import UserAuthDialog
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    print("Firebase authentication disabled - running in standalone mode")

# Import stealth configuration
try:
    from stealth_config import (
        WINDOW_TRANSPARENCY, ENABLE_SCREEN_CAPTURE_HIDING, ENABL<PERSON>_TASKBAR_HIDING,
        ENABLE_CLICK_THROUGH_DEFAULT, ADVANCED_STEALTH_MODE, AUTO_HIDE_ON_SCREEN_SHARE,
        DEBUG_STEALTH_FEATURES, INTERVIEW_SAFETY_MODE, SILENT_ERROR_HANDLING,
        NO_POPUP_DIALOGS, ENHANCED_SCREEN_SHARE_DETECTION, SCREEN_SHARE_CHECK_INTERVAL,
        PANIC_MODE_ENABLED, EMERGENCY_HIDE_HOTKEY, STEALTH_STARTUP_DELAY,
        MULTI_MONITOR_STEALTH, PROCESS_HIDING_ENABLED, PREEMPTIVE_HIDING,
        LOCAL_VISIBILITY_MODE
    )
except ImportError:
    # Default values if config file doesn't exist
    WINDOW_TRANSPARENCY = 0.85
    ENABLE_SCREEN_CAPTURE_HIDING = True
    ENABLE_TASKBAR_HIDING = True
    ENABLE_CLICK_THROUGH_DEFAULT = True
    ADVANCED_STEALTH_MODE = True
    AUTO_HIDE_ON_SCREEN_SHARE = True
    DEBUG_STEALTH_FEATURES = False
    INTERVIEW_SAFETY_MODE = True
    SILENT_ERROR_HANDLING = True
    NO_POPUP_DIALOGS = True
    ENHANCED_SCREEN_SHARE_DETECTION = True
    SCREEN_SHARE_CHECK_INTERVAL = 3.0
    PANIC_MODE_ENABLED = True
    EMERGENCY_HIDE_HOTKEY = "ctrl+shift+h"
    STEALTH_STARTUP_DELAY = 2.0
    MULTI_MONITOR_STEALTH = True
    PROCESS_HIDING_ENABLED = True
    PREEMPTIVE_HIDING = True
    LOCAL_VISIBILITY_MODE = True

class KeyboardSignals(QObject):
    """Signals for keyboard events"""
    arrow_key_signal = pyqtSignal(str)  # direction
    toggle_visibility_signal = pyqtSignal()  # Ctrl+Space toggle

class UISignals(QObject):
    """Signals for UI updates"""
    update_status_signal = pyqtSignal(str)
    update_response_signal = pyqtSignal(str)
    clear_response_signal = pyqtSignal()
    user_data_updated_signal = pyqtSignal(float, dict)  # remaining_seconds, user_data

class InterviewApp(QWidget):
    def __init__(self):
        super().__init__()

        # Initialize Firebase and user authentication (optional)
        if FIREBASE_AVAILABLE:
            self.firebase_manager = FirebaseManager()
            self.current_user_email = None
            self.remaining_time = 0
            self.user_data = None
            self.session_start_time = None
            self.time_update_timer = None

            # Show authentication dialog first
            if not self.authenticate_user():
                sys.exit()
                return
        else:
            # Standalone mode without authentication
            self.firebase_manager = None
            self.current_user_email = "standalone_user"
            self.remaining_time = 3600  # 1 hour default
            self.user_data = {"name": "User", "email": "standalone@local"}
            self.session_start_time = None
            self.time_update_timer = None

        # Initialize keyboard state variables (CapsLock removed for fully automatic mode)
        self.shift_pressed = False
        self.alt_pressed = False
        self.ctrl_pressed = False
        self.space_pressed = False
        self.pressed_keys = set()

        # Initialize window state variables
        self.is_window_visible = True

        # Initialize keyboard signals (CapsLock removed for fully automatic mode)
        self.keyboard_signals = KeyboardSignals()
        self.keyboard_signals.arrow_key_signal.connect(self.handle_arrow_key)
        self.keyboard_signals.toggle_visibility_signal.connect(self.toggle_window_visibility)

        # Initialize UI signals
        self.ui_signals = UISignals()
        self.ui_signals.update_status_signal.connect(self.update_status_bar)
        self.ui_signals.update_response_signal.connect(self._update_streaming_response)
        self.ui_signals.clear_response_signal.connect(self.clear_ai_response)
        self.ui_signals.user_data_updated_signal.connect(self._handle_user_data_update)

        # Window setup with ABID AI exact approach
        self.setWindowTitle("Real-time Interview Assistant")
        # Window flags without click-through
        self.original_flags = Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool
        # Window flags with click-through
        self.click_through_flags = self.original_flags | Qt.WindowTransparentForInput
        # Initialize click-through state
        self.click_through_enabled = False

        self.setWindowFlags(self.original_flags)
        # Removed Qt.WA_TranslucentBackground to fix transparency issue

        # Make window smaller and more compact
        self.resize(500, 400)
        self.setMinimumSize(400, 300)

        # Position window in bottom-right corner of screen
        self.position_window_bottom_right()

        # Setup UI
        self.setup_ui()

        # Start time tracking
        self.start_time_tracking()

        # Setup keyboard listener (ABID AI approach)
        self.setup_keyboard_listener()

        # Apply stealth features
        self.apply_stealth_features()

        # Initialize components (skip UI manager since we're using PyQt5 directly)
        # Enable automatic device detection by default
        self.audio_handler = AudioHandler(self.handle_audio_event, self.get_selected_device_index, auto_device_mode=True)
        try:
            self.api_client = APIClient(ai_indicator_callback=self.update_ai_indicator)
        except ValueError as e:
            if SILENT_ERROR_HANDLING:
                print(f"❌ API Key Error (Silent): {str(e)}")
                # Continue with limited functionality instead of exiting
                self.api_client = None
            else:
                QMessageBox.critical(self, "API Key Error", str(e))
                sys.exit()
                return

        self.populate_audio_devices()

        # Optimized timer for low-end systems - reduced frequency
        self.device_update_timer = QTimer()
        self.device_update_timer.timeout.connect(self.update_device_display)
        self.device_update_timer.start(10000)  # Update every 10 seconds for better performance

        # Enhanced screen sharing detection for interview safety
        if AUTO_HIDE_ON_SCREEN_SHARE:
            self.screen_share_timer = QTimer()
            self.screen_share_timer.timeout.connect(self.check_screen_sharing)
            # Use enhanced detection interval for maximum safety
            interval_ms = int(SCREEN_SHARE_CHECK_INTERVAL * 1000)
            self.screen_share_timer.start(interval_ms)

        # Set initial status message - Fully Automatic Mode
        self.ui_signals.update_status_signal.emit("Status: 🚀 Fully Automatic Mode - Starting continuous speech detection...")

        # Enhanced memory management timer for interview safety
        self.memory_cleanup_timer = QTimer()
        self.memory_cleanup_timer.timeout.connect(self.cleanup_memory)
        self.memory_cleanup_timer.start(20000)  # Clean up memory every 20 seconds for stability

        # Add process stability monitoring
        self.stability_timer = QTimer()
        self.stability_timer.timeout.connect(self.monitor_process_stability)
        self.stability_timer.start(60000)  # Check stability every minute

        # Add resource monitoring
        self.resource_monitor_timer = QTimer()
        self.resource_monitor_timer.timeout.connect(self.monitor_resources)
        self.resource_monitor_timer.start(120000)  # Monitor resources every 2 minutes

    def setup_ui(self):
        """Setup the main UI"""
        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # --- Simplified Controls Frame ---
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
            }
        """)
        controls_layout = QHBoxLayout()

        # AI Response Indicator - Shows which AI is currently responding
        self.ai_indicator = QLabel("🤖 Ready")
        self.ai_indicator.setStyleSheet("""
            QLabel {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 8px 16px;
                border-radius: 5px;
                border: 2px solid #1976D2;
            }
        """)
        controls_layout.addWidget(self.ai_indicator)

        # Add stretch to push close button to the right
        controls_layout.addStretch()

        # Close button
        self.close_button = QPushButton("✕")
        self.close_button.setFixedSize(30, 30)
        self.close_button.clicked.connect(self.close_application)
        self.close_button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 2px solid #d32f2f;
                border-radius: 15px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
                border: 2px solid #b71c1c;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        self.close_button.setToolTip("Close Application")
        controls_layout.addWidget(self.close_button)

        controls_frame.setLayout(controls_layout)

        # --- AI Response Frame (Combined Q&A) ---
        ai_response_frame = QFrame()
        ai_response_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #cccccc;
                border-radius: 5px;
                padding: 5px;
                margin: 2px;
            }
        """)
        ai_response_layout = QVBoxLayout()

        # AI Response header - simplified
        ai_response_label = QLabel("AI Assistant Response")
        ai_response_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333333;
                padding: 5px;
                font-size: 12px;
            }
        """)
        ai_response_layout.addWidget(ai_response_label)

        # AI Response text widget with ABID AI stealth configuration
        self.ai_response_text_widget = QTextEdit()
        self.ai_response_text_widget.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', sans-serif;
                border: 2px solid #333333;
                border-radius: 10px;
                padding: 15px;
                line-height: 1.6;
                selection-background-color: #333333;
                selection-color: #FFFFFF;
                cursor: default;
            }
            QTextEdit:hover {
                cursor: default;
            }
            QTextEdit:focus {
                outline: none;
                border: 2px solid #333333;
                cursor: default;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """)
        self.ai_response_text_widget.setPlainText("🤖 AI interview assistant ready...\n\nI'll provide helpful responses as an expert software developer during your technical interview.")
        self.ai_response_text_widget.setReadOnly(True)

        # Apply ABID AI stealth settings to response area
        self.ai_response_text_widget.setFocusPolicy(Qt.NoFocus)  # Prevent focus to avoid blinking
        self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)  # Disable text interaction

        # Disable cursor blinking completely
        self.ai_response_text_widget.setCursorWidth(0)  # Hide cursor completely
        self.ai_response_text_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.ai_response_text_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Prevent any selection or editing
        self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)
        self.ai_response_text_widget.setContextMenuPolicy(Qt.NoContextMenu)  # Disable right-click menu

        # Additional stealth settings - make response area completely non-interactive
        self.ai_response_text_widget.setEnabled(True)  # Keep enabled but override events
        self.ai_response_text_widget.setAcceptDrops(False)  # No drag and drop

        # Force arrow cursor permanently - this is the key fix
        self.ai_response_text_widget.setCursor(Qt.ArrowCursor)
        self.ai_response_text_widget.viewport().setCursor(Qt.ArrowCursor)  # Also set on viewport

        # Override all mouse and keyboard events to prevent any interaction
        self.ai_response_text_widget.keyPressEvent = lambda event: event.ignore()
        self.ai_response_text_widget.keyReleaseEvent = lambda event: event.ignore()
        self.ai_response_text_widget.focusInEvent = lambda event: self.ai_response_text_widget.clearFocus()
        self.ai_response_text_widget.focusOutEvent = lambda event: None

        # Optimized cursor timer for low-end systems
        self.cursor_timer = QTimer()
        self.cursor_timer.timeout.connect(self.enforce_cursor_behavior)
        self.cursor_timer.start(500)  # Check every 500ms for better performance on low-end systems

        ai_response_layout.addWidget(self.ai_response_text_widget)
        ai_response_frame.setLayout(ai_response_layout)

        # --- Status Bar Frame ---
        bottom_frame = QFrame()
        bottom_layout = QHBoxLayout()

        # Speech Detection Indicator
        self.speech_indicator = QLabel("🔴 Ready")
        self.speech_indicator.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 8px;
                font-weight: bold;
                color: #666;
            }
        """)
        bottom_layout.addWidget(self.speech_indicator)

        self.status_bar = QLabel("Status: Idle")
        self.status_bar.setStyleSheet("""
            QLabel {
                border: 1px solid #cccccc;
                padding: 5px;
                background-color: #f0f0f0;
            }
        """)
        bottom_layout.addWidget(self.status_bar)

        # Add resize handle in bottom right corner
        self.resize_handle = QLabel("⋰")
        self.resize_handle.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-family: Arial;
                padding: 2px;
            }
        """)
        self.resize_handle.setCursor(Qt.SizeFDiagCursor)
        # Make resize handle receive mouse events even with click-through enabled
        self.resize_handle.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        bottom_layout.addWidget(self.resize_handle)
        self.setup_resize_handle()

        bottom_frame.setLayout(bottom_layout)

        # Add all frames to main layout
        layout.addWidget(controls_frame)
        layout.addWidget(ai_response_frame)
        layout.addWidget(bottom_frame)

        self.setLayout(layout)

        # Apply transparency to the main window (ABID AI approach)
        self.setWindowOpacity(WINDOW_TRANSPARENCY)

        # Apply main window styling
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f0f0f0, stop:1 #e0e0e0);
                border-radius: 15px;
            }
        """)

    def authenticate_user(self):
        """Show authentication dialog and validate user"""
        if not FIREBASE_AVAILABLE:
            return True  # Skip authentication in standalone mode

        while True:  # Keep showing dialog until successful authentication or user cancels
            auth_dialog = UserAuthDialog(self)

            # Track if authentication was successful
            auth_successful = False

            def on_user_authenticated(email, remaining_time, user_data):
                nonlocal auth_successful
                self.current_user_email = email
                self.remaining_time = remaining_time
                self.user_data = user_data
                self.session_start_time = datetime.now()
                auth_successful = True
                print(f"✅ User authenticated: {email}, Remaining time: {remaining_time} seconds")

            auth_dialog.user_authenticated.connect(on_user_authenticated)

            # Apply stealth features before showing (but don't show yet)
            auth_dialog.apply_stealth_features()

            result = auth_dialog.exec_()

            if result == auth_dialog.Accepted and auth_successful:
                return True
            elif result == auth_dialog.Rejected:
                # User clicked Cancel or closed dialog
                print("❌ User cancelled authentication")
                return False
            # If result is Accepted but auth_successful is False,
            # it means there was an error, so continue the loop

    def start_time_tracking(self):
        """Start the time tracking timer"""
        if not FIREBASE_AVAILABLE:
            return  # Skip time tracking in standalone mode

        # Update time display every second
        self.time_update_timer = QTimer()
        self.time_update_timer.timeout.connect(self.update_time_display)
        self.time_update_timer.start(1000)  # Update every second

        # Update Firebase every 30 seconds
        self.firebase_update_timer = QTimer()
        self.firebase_update_timer.timeout.connect(self.update_firebase_time)
        self.firebase_update_timer.start(30000)  # Update every 30 seconds

        # Start real-time listener for user data updates
        if self.current_user_email and self.firebase_manager:
            self.firebase_manager.start_user_listener(self.current_user_email, self.on_user_data_updated)

    def update_time_display(self):
        """Update the time display in UI"""
        if self.session_start_time and self.remaining_time > 0:
            # Calculate elapsed time
            elapsed_time = (datetime.now() - self.session_start_time).total_seconds()
            current_remaining = max(0, self.remaining_time - elapsed_time)

            if current_remaining <= 0:
                # Time expired
                self.handle_time_expired()
                return

            # Format time display
            time_str = self.firebase_manager.format_time(current_remaining)

            # Update AI indicator with time
            if current_remaining > 3600:  # More than 1 hour
                self.ai_indicator.setText(f"🤖 Ready | ⏰ {time_str}")
            elif current_remaining > 300:  # More than 5 minutes
                self.ai_indicator.setText(f"🤖 Ready | ⏰ {time_str}")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #FF9800;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #F57C00;
                    }
                """)
            else:  # Less than 5 minutes - critical
                self.ai_indicator.setText(f"🤖 Ready | ⚠️ {time_str}")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #f44336;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #d32f2f;
                    }
                """)

    def update_firebase_time(self):
        """Update used time in Firebase"""
        if self.session_start_time and self.current_user_email:
            elapsed_time = (datetime.now() - self.session_start_time).total_seconds()
            self.firebase_manager.update_user_time(self.current_user_email, elapsed_time)
            # Reset session start time for next interval
            self.session_start_time = datetime.now()

    def on_user_data_updated(self, remaining_seconds, user_data):
        """Handle real-time user data updates from Firebase (called from Firebase thread)"""
        try:
            print(f"🔄 Real-time update: Remaining time = {remaining_seconds} seconds")
            # Emit signal to handle in main thread
            self.ui_signals.user_data_updated_signal.emit(remaining_seconds, user_data)

        except Exception as e:
            print(f"❌ Error handling user data update: {e}")

    def _handle_user_data_update(self, remaining_seconds, user_data):
        """Handle user data updates in main thread (thread-safe)"""
        try:
            # Update remaining time
            old_remaining = self.remaining_time
            self.remaining_time = remaining_seconds
            self.user_data = user_data

            # If time was added, reset session start time to current time
            # This prevents the local timer from immediately consuming the added time
            if remaining_seconds > old_remaining:
                print(f"✅ Time added! Old: {old_remaining}s, New: {remaining_seconds}s")
                self.session_start_time = datetime.now()

            # Update UI immediately
            self.update_time_display()

            # Check if time expired
            if remaining_seconds <= 0:
                self.handle_time_expired()

        except Exception as e:
            print(f"❌ Error handling user data update in main thread: {e}")

    def handle_time_expired(self):
        """Handle when user's time has expired"""
        # Stop timers
        if self.time_update_timer:
            self.time_update_timer.stop()
        if self.firebase_update_timer:
            self.firebase_update_timer.stop()

        # Stop Firebase listener
        if hasattr(self, 'firebase_manager') and self.firebase_manager:
            self.firebase_manager.stop_user_listener()

        # Update AI indicator
        self.ai_indicator.setText("❌ Time Expired")
        self.ai_indicator.setStyleSheet("""
            QLabel {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 8px 16px;
                border-radius: 5px;
                border: 2px solid #d32f2f;
            }
        """)

        # Show time expired message in response area
        expired_message = """
❌ TIME EXPIRED ❌

Your allocated time has finished.

For more time, please contact:
📞 Abid Ansari: 8104184175

Thank you for using AI Assistant!
        """

        self.ai_response.setText(expired_message)
        self.ai_response.setStyleSheet("""
            QTextEdit {
                background-color: #ffebee;
                border: 3px solid #f44336;
                border-radius: 10px;
                padding: 15px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                color: #d32f2f;
                font-weight: bold;
            }
        """)

        # Disable audio handler
        if hasattr(self, 'audio_handler'):
            self.audio_handler.stop_listening()

        # Final Firebase update
        if self.session_start_time and self.current_user_email:
            elapsed_time = (datetime.now() - self.session_start_time).total_seconds()
            self.firebase_manager.update_user_time(self.current_user_email, elapsed_time)

    def hide_from_capture(self):
        """Enhanced hide window from screen capture while keeping it visible locally"""
        try:
            hwnd = int(self.winId())

            # PRIMARY METHOD: Set window to be excluded from capture (Windows 10+)
            # This is the most effective method that hides from screen capture while keeping local visibility
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result1 = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            # SECONDARY METHOD: Apply minimal layered window style for additional protection
            # Only apply if LOCAL_VISIBILITY_MODE allows it
            if not LOCAL_VISIBILITY_MODE:
                GWL_EXSTYLE = -20
                WS_EX_LAYERED = 0x00080000
                WS_EX_NOACTIVATE = 0x08000000

                # Get current extended style
                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)

                # Add minimal styles that don't affect local visibility
                new_style = current_style | WS_EX_LAYERED | WS_EX_NOACTIVATE
                result2 = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)
            else:
                result2 = True  # Skip this method to maintain local visibility

            # AVOID DWM cloaking in local visibility mode as it can hide the window completely
            result3 = True  # Skip DWM cloaking to maintain local visibility

            # Store hwnd for later use
            self.hwnd = hwnd

            if result1:
                if LOCAL_VISIBILITY_MODE:
                    print("✅ Window hidden from screen capture while remaining visible locally")
                    print("   • Display affinity exclusion applied (primary method)")
                    print("   • Local visibility maintained for user interaction")
                else:
                    print("✅ Window successfully hidden from screen capture (Enhanced)")
                    print(f"   • Multiple hiding methods applied successfully")
            else:
                print(f"⚠️ Screen capture hiding may not be fully effective")

        except Exception as e:
            print(f"❌ Error hiding from capture: {e}")

    def hide_from_taskbar(self):
        """Hide window from taskbar"""
        try:
            if ENABLE_TASKBAR_HIDING:
                hwnd = int(self.winId())

                # Hide from taskbar by setting as tool window
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW
                result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if result:
                    print("✅ Window hidden from taskbar")
                else:
                    print("❌ Failed to hide window from taskbar")
        except Exception as e:
            print(f"❌ Error hiding from taskbar: {e}")

    def make_window_invisible_to_capture(self):
        """Make window completely invisible during screen capture"""
        try:
            if hasattr(self, 'hwnd'):
                # Move window far off-screen during capture
                ctypes.windll.user32.SetWindowPos(
                    self.hwnd, 0, -10000, -10000, 0, 0,
                    0x0001 | 0x0004  # SWP_NOSIZE | SWP_NOZORDER
                )
                print("🥷 Window moved off-screen for capture hiding")
        except Exception as e:
            print(f"❌ Error making window invisible: {e}")

    def restore_window_position(self):
        """Restore window to normal position after capture"""
        try:
            if hasattr(self, 'hwnd'):
                # Restore to bottom-right position
                self.position_window_bottom_right()
                print("🔄 Window position restored")
        except Exception as e:
            print(f"❌ Error restoring window position: {e}")

    def apply_stealth_features(self):
        """Apply all stealth features based on configuration"""
        if DEBUG_STEALTH_FEATURES:
            print("🥷 Applying stealth features...")

        # Apply startup delay for stealth initialization
        if STEALTH_STARTUP_DELAY > 0:
            time.sleep(STEALTH_STARTUP_DELAY)

        # Hide from screen capture
        if ENABLE_SCREEN_CAPTURE_HIDING:
            self.hide_from_capture()

        # Hide from taskbar
        if ENABLE_TASKBAR_HIDING:
            self.hide_from_taskbar()

        # Set window transparency
        self.setWindowOpacity(WINDOW_TRANSPARENCY)

        # Enable click-through by default if configured
        if ENABLE_CLICK_THROUGH_DEFAULT:
            self.toggle_click_through()

        # Apply advanced stealth mode
        if ADVANCED_STEALTH_MODE:
            self.apply_advanced_stealth()

        # Apply interview safety mode enhancements
        if INTERVIEW_SAFETY_MODE:
            self.apply_interview_safety_mode()

        # Apply process hiding if enabled
        if PROCESS_HIDING_ENABLED:
            self.apply_process_hiding()

        # Apply multi-monitor stealth if enabled
        if MULTI_MONITOR_STEALTH:
            self.apply_multi_monitor_stealth()

        if DEBUG_STEALTH_FEATURES:
            print("✅ All stealth features applied successfully")

    def apply_advanced_stealth(self):
        """Apply advanced stealth techniques while maintaining local visibility"""
        try:
            hwnd = int(self.winId())

            # Only apply advanced stealth if local visibility mode is disabled
            if not LOCAL_VISIBILITY_MODE:
                # Make window non-enumerable (harder to detect by other applications)
                GWL_EXSTYLE = -20
                WS_EX_NOACTIVATE = 0x08000000
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_NOACTIVATE | WS_EX_TOOLWINDOW

                result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                # Set window to bottom of Z-order
                HWND_BOTTOM = 1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_NOACTIVATE = 0x0010

                ctypes.windll.user32.SetWindowPos(
                    hwnd, HWND_BOTTOM, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE
                )

                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Advanced stealth mode applied")
            else:
                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Advanced stealth skipped to maintain local visibility")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying advanced stealth: {e}")

    def check_screen_sharing(self):
        """Enhanced screen sharing detection for Windows 10 compatibility"""
        try:
            import psutil

            # Expanded screen sharing process names for better detection
            screen_share_processes = [
                'zoom.exe', 'teams.exe', 'skype.exe', 'discord.exe',
                'obs64.exe', 'obs32.exe', 'streamlabs obs.exe',
                'webexmta.exe', 'gotomeeting.exe', 'anydesk.exe',
                'teamviewer.exe', 'chrome.exe', 'firefox.exe',
                'msedge.exe', 'opera.exe', 'brave.exe', 'vivaldi.exe',
                'microsoftedge.exe', 'microsoftedgecp.exe',
                'webex.exe', 'slack.exe', 'whatsapp.exe',
                'telegram.exe', 'signal.exe', 'meet.exe'
            ]

            # Check if any screen sharing process is running
            screen_sharing_detected = False
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    process_name = proc.info['name'].lower()
                    if any(share_proc in process_name for share_proc in screen_share_processes):
                        screen_sharing_detected = True
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if screen_sharing_detected:
                # Screen sharing detected - apply enhanced stealth for Windows 10
                self.apply_enhanced_stealth_win10()
            else:
                # No screen sharing - restore normal stealth
                self.restore_normal_stealth()

        except ImportError:
            # psutil not available, apply maximum stealth as fallback
            self.apply_enhanced_stealth_win10()
        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"⚠️ Screen sharing detection error: {e}")
            # On error, apply maximum stealth for safety
            self.apply_enhanced_stealth_win10()

    def apply_enhanced_stealth_win10(self):
        """Enhanced stealth for Windows 10 while maintaining local visibility"""
        try:
            if hasattr(self, 'hwnd') and not LOCAL_VISIBILITY_MODE:
                # Only apply if local visibility mode is disabled
                # Method 1: Move window far off-screen (beyond any monitor)
                ctypes.windll.user32.SetWindowPos(
                    self.hwnd, 0, -50000, -50000, 0, 0,
                    0x0001 | 0x0004 | 0x0010  # SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE
                )

                # Method 2: Make window completely transparent
                self.setWindowOpacity(0.001)  # Nearly invisible

                # Method 3: Windows 10 specific - Set window as cloaked
                DWM_CLOAKED = 14
                cloaked_value = ctypes.c_int(1)
                try:
                    ctypes.windll.dwmapi.DwmSetWindowAttribute(
                        self.hwnd, DWM_CLOAKED, ctypes.byref(cloaked_value), ctypes.sizeof(cloaked_value)
                    )
                except:
                    pass  # Ignore if DWM API not available

                # Method 4: Set window to bottom of Z-order and make it non-enumerable
                HWND_BOTTOM = 1
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                SWP_NOACTIVATE = 0x0010
                SWP_HIDEWINDOW = 0x0080

                ctypes.windll.user32.SetWindowPos(
                    self.hwnd, HWND_BOTTOM, 0, 0, 0, 0,
                    SWP_NOSIZE | SWP_NOMOVE | SWP_NOACTIVATE | SWP_HIDEWINDOW
                )

                if DEBUG_STEALTH_FEATURES:
                    print("🥷 Enhanced Windows 10 stealth applied - Maximum hiding")
            elif LOCAL_VISIBILITY_MODE:
                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Enhanced stealth skipped to maintain local visibility")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying enhanced Windows 10 stealth: {e}")

    def restore_normal_stealth(self):
        """Restore normal stealth mode when screen sharing is not detected"""
        try:
            if hasattr(self, 'hwnd'):
                # Restore to normal position but keep stealth features
                self.position_window_bottom_right()

                # Restore normal transparency
                self.setWindowOpacity(WINDOW_TRANSPARENCY)

                # Remove cloaking if it was applied
                DWM_CLOAKED = 14
                cloaked_value = ctypes.c_int(0)
                try:
                    ctypes.windll.dwmapi.DwmSetWindowAttribute(
                        self.hwnd, DWM_CLOAKED, ctypes.byref(cloaked_value), ctypes.sizeof(cloaked_value)
                    )
                except:
                    pass  # Ignore if DWM API not available

                if DEBUG_STEALTH_FEATURES:
                    print("🔄 Normal stealth mode restored")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error restoring normal stealth: {e}")

    def apply_interview_safety_mode(self):
        """Apply interview safety mode enhancements"""
        try:
            if hasattr(self, 'hwnd'):
                # Additional safety measures for interviews
                hwnd = self.hwnd

                # Set window to be excluded from Alt+Tab
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080
                WS_EX_NOACTIVATE = 0x08000000

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE
                ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Interview safety mode applied")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying interview safety mode: {e}")

    def apply_process_hiding(self):
        """Apply process hiding techniques"""
        try:
            # Hide from process enumeration where possible
            if hasattr(self, 'hwnd'):
                hwnd = self.hwnd

                # Set window as a system window to reduce visibility
                GWL_EXSTYLE = -20
                WS_EX_TOOLWINDOW = 0x00000080

                current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                new_style = current_style | WS_EX_TOOLWINDOW
                ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Process hiding applied")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying process hiding: {e}")

    def apply_multi_monitor_stealth(self):
        """Apply enhanced stealth for multi-monitor setups"""
        try:
            # Ensure window is positioned correctly across multiple monitors
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.geometry()
                # Position window in a less visible area
                x = screen_geometry.width() - self.width() - 10
                y = screen_geometry.height() - self.height() - 50
                self.move(x, y)

                if DEBUG_STEALTH_FEATURES:
                    print("🔒 Multi-monitor stealth applied")

        except Exception as e:
            if DEBUG_STEALTH_FEATURES:
                print(f"❌ Error applying multi-monitor stealth: {e}")

    def _attempt_audio_recovery(self):
        """Attempt to recover from audio errors silently"""
        try:
            if hasattr(self, 'audio_handler') and self.audio_handler:
                # Try to restart audio handler
                if self.audio_handler.is_listening:
                    self.audio_handler.stop_listening()

                # Small delay before restart
                time.sleep(1.0)

                # Attempt to restart
                if hasattr(self.audio_handler, 'start_continuous_listening'):
                    self.audio_handler.start_continuous_listening()

                print("🔄 Audio recovery attempted")

        except Exception as e:
            print(f"❌ Audio recovery failed: {e}")

    def enforce_cursor_behavior(self):
        """Periodically enforce arrow cursor behavior on response area - ABID AI approach"""
        try:
            if hasattr(self, 'ai_response_text_widget') and self.ai_response_text_widget:
                # Always force arrow cursor on both widget and viewport
                self.ai_response_text_widget.setCursor(Qt.ArrowCursor)
                self.ai_response_text_widget.viewport().setCursor(Qt.ArrowCursor)

                # Always ensure no focus to prevent blinking
                if self.ai_response_text_widget.hasFocus():
                    self.ai_response_text_widget.clearFocus()

                # Ensure text interaction is disabled
                self.ai_response_text_widget.setTextInteractionFlags(Qt.NoTextInteraction)

                # Ensure focus policy is maintained
                self.ai_response_text_widget.setFocusPolicy(Qt.NoFocus)

        except Exception as e:
            # Silently handle errors to avoid spam
            pass

    def populate_audio_devices(self):
        """Simplified audio device handling - no UI updates needed"""
        pass

    def update_device_display(self):
        """Simplified device display update"""
        pass

    def cleanup_memory(self):
        """Enhanced memory cleanup for interview safety and performance"""
        try:
            # Force garbage collection
            gc.collect()

            # Clear API client cache if it exists
            if hasattr(self, 'api_client') and self.api_client and hasattr(self.api_client, 'response_cache'):
                # Keep only recent cache entries (last 5 for memory efficiency)
                if len(self.api_client.response_cache) > 5:
                    sorted_items = sorted(self.api_client.response_cache.items(),
                                        key=lambda x: x[1][1], reverse=True)  # Sort by timestamp, newest first
                    # Keep only the 5 most recent entries
                    self.api_client.response_cache = dict(sorted_items[:5])

                # Reset failed APIs periodically to allow retry
                self.api_client.failed_apis.clear()

            # Clear audio processing segments
            if hasattr(self, 'audio_handler') and self.audio_handler:
                if hasattr(self.audio_handler, 'processing_segments'):
                    # Clear old processing segments
                    current_time = time.time()
                    old_segments = [seg_id for seg_id in self.audio_handler.processing_segments
                                  if current_time - seg_id/1000 > 300]  # 5 minutes old
                    for seg_id in old_segments:
                        self.audio_handler.processing_segments.discard(seg_id)

            # Clear any temporary variables
            if hasattr(self, 'current_question'):
                # Keep only the most recent question
                pass  # Keep current question for context

            # Force another garbage collection after cleanup
            gc.collect()

            if DEBUG_STEALTH_FEATURES:
                print("🧹 Memory cleanup completed")

        except Exception as e:
            # Silently handle cleanup errors during interviews
            if not SILENT_ERROR_HANDLING:
                print(f"❌ Memory cleanup error: {e}")

    def monitor_process_stability(self):
        """Monitor process stability and recover from issues"""
        try:
            # Check if audio handler is still responsive
            if hasattr(self, 'audio_handler') and self.audio_handler:
                # Check if audio handler has been stuck for too long
                if hasattr(self.audio_handler, 'last_successful_transcription_time'):
                    current_time = time.time()
                    last_success = self.audio_handler.last_successful_transcription_time
                    if last_success > 0 and current_time - last_success > 600:  # 10 minutes
                        if DEBUG_STEALTH_FEATURES:
                            print("🔄 Audio handler appears stuck, attempting recovery...")
                        self._attempt_audio_recovery()

            # Check if API client is responsive
            if hasattr(self, 'api_client') and self.api_client:
                # Reset failed APIs periodically to allow retry
                if hasattr(self.api_client, 'failed_apis') and len(self.api_client.failed_apis) >= 3:
                    self.api_client.failed_apis.clear()
                    if DEBUG_STEALTH_FEATURES:
                        print("🔄 Reset failed APIs for stability")

            # Force garbage collection for stability
            gc.collect()

        except Exception as e:
            if not SILENT_ERROR_HANDLING:
                print(f"❌ Process stability monitoring error: {e}")

    def monitor_resources(self):
        """Monitor system resources and optimize if needed"""
        try:
            import psutil

            # Get current process
            process = psutil.Process()

            # Check memory usage
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            # If memory usage is high, trigger aggressive cleanup
            if memory_mb > 200:  # 200MB threshold
                if DEBUG_STEALTH_FEATURES:
                    print(f"🧹 High memory usage detected: {memory_mb:.1f}MB, triggering cleanup")
                self.cleanup_memory()
                # Additional aggressive cleanup
                gc.collect()
                gc.collect()  # Double collection for thorough cleanup

            # Check CPU usage
            cpu_percent = process.cpu_percent()
            if cpu_percent > 50:  # High CPU usage
                if DEBUG_STEALTH_FEATURES:
                    print(f"⚡ High CPU usage detected: {cpu_percent:.1f}%")
                # Reduce timer frequencies temporarily
                if hasattr(self, 'memory_cleanup_timer'):
                    self.memory_cleanup_timer.setInterval(40000)  # Slower cleanup

        except ImportError:
            # psutil not available, skip resource monitoring
            pass
        except Exception as e:
            if not SILENT_ERROR_HANDLING:
                print(f"❌ Resource monitoring error: {e}")

    def update_ai_indicator(self, api_name, status="processing"):
        """Update the AI indicator to show which service is responding"""
        if status == "processing":
            if api_name == "gemini":
                self.ai_indicator.setText("🤖 Gemini Responding...")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #4285F4;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #3367D6;
                    }
                """)
            elif api_name == "mistral":
                self.ai_indicator.setText("🧠 Mistral Responding...")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #FF6B35;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #E55A2B;
                    }
                """)
            elif api_name == "openrouter":
                self.ai_indicator.setText("🔀 OpenRouter Responding...")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #8B5CF6;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #7C3AED;
                    }
                """)
            elif api_name == "openai":
                self.ai_indicator.setText("🚀 OpenAI Responding...")
                self.ai_indicator.setStyleSheet("""
                    QLabel {
                        background-color: #10A37F;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: 2px solid #0D8A6B;
                    }
                """)
        elif status == "ready":
            self.ai_indicator.setText("🤖 Ready")
            self.ai_indicator.setStyleSheet("""
                QLabel {
                    background-color: #2196F3;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 8px 16px;
                    border-radius: 5px;
                    border: 2px solid #1976D2;
                }
            """)

    def get_selected_device_index(self):
        """Always use auto device detection"""
        return self.audio_handler.get_current_device_index()

    def handle_audio_event(self, event_type, data):
        """Callback function for AudioHandler events."""
        if event_type == "transcription":
            # Store the question for Q&A format
            if data and not data.startswith("[") and len(data.strip()) > 3: # Improved validation
                self.current_question = data
                print(f"📝 Received transcription: {data}")
                # Run API call in a new thread to avoid blocking UI
                threading.Thread(target=self.fetch_ai_response, args=(data,), daemon=True).start()
            else:
                print(f"⚠️ Skipping invalid transcription: {data}")
        elif event_type == "status":
            self.ui_signals.update_status_signal.emit(data)
        elif event_type == "error":
            print(f"❌ Audio error: {data}")
            if SILENT_ERROR_HANDLING:
                # Handle errors silently during interviews
                self.ui_signals.update_status_signal.emit("Status: Ready")
                print(f"🔇 Silent error handling: {data}")
                # Attempt to recover automatically
                if "device" in data.lower() or "stream" in data.lower():
                    self._attempt_audio_recovery()
            else:
                self.ui_signals.update_status_signal.emit(f"Status: Error - {data}")
                # Only show critical errors to user, not every minor issue
                if "device" in data.lower() or "stream" in data.lower():
                    QMessageBox.critical(self, "Audio Error", data)
                    self.stop_listening_ui_update()

    def update_status_bar(self, text):
        """Update status bar text"""
        self.status_bar.setText(text)

        # Update speech indicator based on status
        if "Listening" in text:
            self.update_speech_indicator("listening")
        elif "Processing" in text or "Analyzing" in text:
            self.update_speech_indicator("processing")
        elif "Ready" in text or "Automatic Mode" in text:
            self.update_speech_indicator("ready")
        elif "Error" in text:
            self.update_speech_indicator("error")

    def update_speech_indicator(self, state):
        """Update the speech detection indicator"""
        if state == "listening":
            self.speech_indicator.setText("🎤 Listening")
            self.speech_indicator.setStyleSheet("""
                QLabel {
                    background-color: #4CAF50;
                    border: 1px solid #45a049;
                    border-radius: 3px;
                    padding: 2px 8px;
                    font-weight: bold;
                    color: white;
                }
            """)
        elif state == "processing":
            self.speech_indicator.setText("⚡ Processing")
            self.speech_indicator.setStyleSheet("""
                QLabel {
                    background-color: #FF9800;
                    border: 1px solid #f57c00;
                    border-radius: 3px;
                    padding: 2px 8px;
                    font-weight: bold;
                    color: white;
                }
            """)
        elif state == "ready":
            self.speech_indicator.setText("✅ Ready")
            self.speech_indicator.setStyleSheet("""
                QLabel {
                    background-color: #2196F3;
                    border: 1px solid #1976D2;
                    border-radius: 3px;
                    padding: 2px 8px;
                    font-weight: bold;
                    color: white;
                }
            """)
        elif state == "error":
            self.speech_indicator.setText("❌ Error")
            self.speech_indicator.setStyleSheet("""
                QLabel {
                    background-color: #f44336;
                    border: 1px solid #d32f2f;
                    border-radius: 3px;
                    padding: 2px 8px;
                    font-weight: bold;
                    color: white;
                }
            """)

    def clear_ai_response(self):
        """Clear AI response text widget"""
        self.ai_response_text_widget.setReadOnly(False)
        self.ai_response_text_widget.clear()
        self.ai_response_text_widget.setReadOnly(True)

    def fetch_ai_response(self, text):
        """Optimized AI response fetching for low-end systems"""
        # Check if user has time remaining
        if self.session_start_time and self.remaining_time > 0:
            elapsed_time = (datetime.now() - self.session_start_time).total_seconds()
            current_remaining = max(0, self.remaining_time - elapsed_time)

            if current_remaining <= 0:
                self.handle_time_expired()
                return

        # Use signals to ensure UI updates happen in main thread
        self.ui_signals.update_status_signal.emit("Status: Analyzing question...")

        # Show question immediately for faster response
        self.current_question = text
        question_text = f"🎤 Question: {text}\n\n💭 Thinking...\n"
        self.ui_signals.update_response_signal.emit(question_text)

        # Define streaming callback for real-time updates
        def streaming_callback(partial_response):
            # Update UI with streaming response (AI name already included in partial_response)
            full_text = f"🎤 Question: {text}\n\n✅ {partial_response}"
            self.ui_signals.update_response_signal.emit(full_text)

        try:
            # Get AI response with streaming - optimized for interview safety
            print(f"🤖 Requesting AI response for: {text[:50]}...")

            # Add small delay to prevent overwhelming low-end systems
            time.sleep(0.1)

            # Handle case where API client is None (silent error handling)
            if self.api_client is None:
                if SILENT_ERROR_HANDLING:
                    ai_response = "I understand your question. Let me provide a thoughtful response based on my experience."
                else:
                    ai_response = "API client not available. Please check configuration."
            else:
                ai_response = self.api_client.get_ai_response(text, streaming_callback)

            # Update with final response
            if ai_response and not ai_response.startswith("["):
                final_text = f"🎤 Question: {text}\n\n✅ {ai_response}"
                self.ui_signals.update_response_signal.emit(final_text)
                print(f"✅ AI response delivered successfully")
            elif ai_response:
                # Handle error responses silently during interviews
                if SILENT_ERROR_HANDLING and ai_response.startswith("["):
                    # Convert error to helpful response
                    helpful_text = f"🎤 Question: {text}\n\n✅ (Gemini) Answer:\nI need a moment to process that question properly. Could you please rephrase it or provide more context?"
                    self.ui_signals.update_response_signal.emit(helpful_text)
                    print(f"🔇 Silent error handling: {ai_response}")
                else:
                    error_text = f"🎤 Question: {text}\n\n❌ Error:\n{ai_response}"
                    self.ui_signals.update_response_signal.emit(error_text)
                    print(f"❌ AI response error: {ai_response}")
            else:
                # No response received
                if SILENT_ERROR_HANDLING:
                    helpful_text = f"🎤 Question: {text}\n\n✅ (Gemini) Answer:\nI apologize, but I need you to repeat the question. I want to make sure I give you the best answer."
                    self.ui_signals.update_response_signal.emit(helpful_text)
                else:
                    error_text = f"🎤 Question: {text}\n\n❌ No response received from AI"
                    self.ui_signals.update_response_signal.emit(error_text)
                print(f"❌ No AI response received")

        except Exception as e:
            print(f"❌ Error fetching AI response: {e}")
            if SILENT_ERROR_HANDLING:
                # Provide helpful response instead of error during interviews
                helpful_text = f"🎤 Question: {text}\n\n✅ (Gemini) Answer:\nI'm processing your question. Could you please repeat it or ask it in a different way?"
                self.ui_signals.update_response_signal.emit(helpful_text)
                print(f"🔇 Silent exception handling: {e}")
            else:
                error_text = f"🎤 Question: {text}\n\n❌ Error getting response: {str(e)}"
                self.ui_signals.update_response_signal.emit(error_text)
        finally:
            # Force garbage collection after AI response to free memory
            gc.collect()

        # Update status only if not currently listening (to avoid overwriting listening status)
        if not self.audio_handler.is_listening:
            self.ui_signals.update_status_signal.emit("Status: Ready for next question")
        else:
            self.ui_signals.update_status_signal.emit("Status: Listening... (Response ready)")

    def closeEvent(self, event):
        """Handle application close event"""
        # Final time update to Firebase
        if self.session_start_time and self.current_user_email:
            elapsed_time = (datetime.now() - self.session_start_time).total_seconds()
            self.firebase_manager.update_user_time(self.current_user_email, elapsed_time)
            print(f"✅ Final time update: {elapsed_time:.2f} seconds")

        # Stop Firebase listener
        if hasattr(self, 'firebase_manager') and self.firebase_manager:
            self.firebase_manager.stop_user_listener()

        # Stop timers
        if hasattr(self, 'time_update_timer') and self.time_update_timer:
            self.time_update_timer.stop()
        if hasattr(self, 'firebase_update_timer') and self.firebase_update_timer:
            self.firebase_update_timer.stop()

        # Stop audio handler
        if hasattr(self, 'audio_handler'):
            self.audio_handler.stop_listening()

        event.accept()

    def _update_response_text(self, text):
        """Thread-safe method to update response text."""
        try:
            self.ai_response_text_widget.setReadOnly(False)
            self.ai_response_text_widget.clear()
            self.ai_response_text_widget.setPlainText(text)
            # Scroll to bottom
            cursor = self.ai_response_text_widget.textCursor()
            cursor.movePosition(cursor.End)
            self.ai_response_text_widget.setTextCursor(cursor)
            self.ai_response_text_widget.setReadOnly(True)
        except Exception as e:
            # Silently handle threading issues
            pass

    def _update_streaming_response(self, text):
        """Thread-safe method to update streaming response."""
        try:
            self.ai_response_text_widget.setReadOnly(False)
            self.ai_response_text_widget.clear()
            self.ai_response_text_widget.setPlainText(text)
            # Scroll to bottom
            cursor = self.ai_response_text_widget.textCursor()
            cursor.movePosition(cursor.End)
            self.ai_response_text_widget.setTextCursor(cursor)
            self.ai_response_text_widget.setReadOnly(True)
        except Exception as e:
            # Silently handle errors
            pass

    # Manual listening methods removed - Fully Automatic Mode Only
    def start_listening(self):
        """Legacy method - not used in fully automatic mode"""
        print("⚠️ Manual start_listening called but system is in fully automatic mode")
        pass

    def stop_listening_ui_update(self):
        """Legacy method - not used in fully automatic mode"""
        print("⚠️ Manual stop_listening_ui_update called but system is in fully automatic mode")
        pass

    def stop_listening(self):
        """Legacy method - not used in fully automatic mode"""
        print("⚠️ Manual stop_listening called but system is in fully automatic mode")
        pass

    def position_window_bottom_right(self):
        """Position window in bottom-right corner of screen"""
        # Get screen dimensions
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # Get window dimensions
        window_width = 500
        window_height = 400

        # Calculate position (bottom-right with some margin)
        x = screen_width - window_width - 50
        y = screen_height - window_height - 100

        self.move(x, y)

    def setup_keyboard_listener(self):
        """Setup global keyboard listener - ABID AI approach"""
        try:
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.keyboard_listener.start()
            print("✅ Keyboard listener started")
        except Exception as e:
            print(f"❌ Error starting keyboard listener: {e}")

    def on_key_press(self, key):
        """Handle key press events - ABID AI approach"""
        try:
            print(f"Key pressed: {key}")  # Debug output

            # Initialize pressed_keys set if it doesn't exist
            if not hasattr(self, 'pressed_keys'):
                self.pressed_keys = set()

            # Add key to pressed keys set
            self.pressed_keys.add(key)

            # Shift detection
            if key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = True
                print("Shift key pressed")

            # Ctrl detection
            elif key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = True
                print("Ctrl key pressed")

            # Space detection for Ctrl+Space combination
            elif key == keyboard.Key.space:
                self.space_pressed = True
                if self.ctrl_pressed:
                    print("🔄 Ctrl+Space pressed - Toggling window visibility")
                    self.keyboard_signals.toggle_visibility_signal.emit()

            # CapsLock functionality removed - fully automatic mode only

            # Arrow keys - check if Ctrl is pressed
            elif key == keyboard.Key.up:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('up')

            elif key == keyboard.Key.down:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('down')

            elif key == keyboard.Key.left:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('left')

            elif key == keyboard.Key.right:
                if self.ctrl_pressed:
                    self.keyboard_signals.arrow_key_signal.emit('right')

        except Exception as e:
            print(f"❌ Error in key press: {e}")

    def on_key_release(self, key):
        """Handle key release events - ABID AI approach"""
        try:
            # Remove key from pressed keys set
            if hasattr(self, 'pressed_keys') and key in self.pressed_keys:
                self.pressed_keys.remove(key)

            # Update key state flags (CapsLock removed for fully automatic mode)
            if key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = False
            elif key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = False
                print("Ctrl key released")
            elif key == keyboard.Key.space:
                self.space_pressed = False
        except Exception as e:
            print(f"❌ Error in key release: {e}")

    # CapsLock functionality removed - fully automatic mode only

    def handle_arrow_key(self, direction):
        """Handle arrow key press - move window"""
        print(f"🔄 Arrow key signal received - Moving window {direction}")
        self.move_window(direction)

    def toggle_window_visibility(self):
        """Toggle window visibility with Ctrl+Space"""
        if self.is_window_visible:
            self.hide_window()
        else:
            self.show_window()

    def hide_window(self):
        """Hide the window or move it to back"""
        print("🔄 Hiding window...")
        self.is_window_visible = False
        # Option 1: Hide the window completely
        self.hide()
        # Option 2: Alternative - lower the window (move to back)
        # self.lower()
        # self.setWindowOpacity(0.1)  # Make nearly transparent

    def show_window(self):
        """Show the window"""
        print("🔄 Showing window...")
        self.is_window_visible = True
        self.show()
        self.raise_()  # Bring to front
        self.activateWindow()  # Give focus
        self.setWindowOpacity(WINDOW_TRANSPARENCY)  # Restore original transparency

    def toggle_click_through(self, enable=None):
        """Toggle click-through functionality"""
        if enable is None:
            self.click_through_enabled = not self.click_through_enabled
        else:
            self.click_through_enabled = enable

        if self.click_through_enabled:
            print("🔄 Enabling click-through...")
            self.setWindowFlags(self.click_through_flags)
            self.show()  # Need to show after changing flags
            self.ui_signals.update_status_signal.emit("Status: Click-through enabled (default)")
        else:
            print("🔄 Disabling click-through...")
            self.setWindowFlags(self.original_flags)
            self.show()  # Need to show after changing flags
            self.ui_signals.update_status_signal.emit("Status: Click-through disabled")

    def move_window(self, direction):
        """Move window in the specified direction using Ctrl+Arrow keys"""
        # Get current window position
        current_x = self.x()
        current_y = self.y()

        # Get screen dimensions for boundary checking
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # Get window dimensions
        window_width = self.width()
        window_height = self.height()

        # Movement step size (pixels)
        step = 50

        # Calculate new position based on direction
        new_x = current_x
        new_y = current_y

        if direction == 'up':
            new_y = max(0, current_y - step)
        elif direction == 'down':
            new_y = min(screen_height - window_height, current_y + step)
        elif direction == 'left':
            new_x = max(0, current_x - step)
        elif direction == 'right':
            new_x = min(screen_width - window_width, current_x + step)

        # Apply new position
        self.move(new_x, new_y)

        # Update status bar to show movement using signals
        self.ui_signals.update_status_signal.emit(f"Status: Window moved {direction}")

    def toggle_listening(self):
        """Legacy method - not used in fully automatic mode"""
        print("⚠️ Manual toggle_listening called but system is in fully automatic mode")
        print("🤖 System is in continuous automatic speech detection mode")
        pass

    # Toggle functionality removed - Fully Automatic Mode Only

    def setup_resize_handle(self):
        """Setup resize handle functionality"""
        # Variables for resize operation
        self.resize_start_x = 0
        self.resize_start_y = 0
        self.resize_start_width = 0
        self.resize_start_height = 0
        self.is_resizing = False

        # Create a custom resize handle widget
        self.resize_handle.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Event filter for resize handle"""
        if obj == self.resize_handle:
            if event.type() == event.MouseButtonPress:
                if event.button() == Qt.LeftButton:
                    self.start_resize(event)
                    return True
            elif event.type() == event.MouseMove:
                if self.is_resizing:
                    self.do_resize(event)
                    return True
            elif event.type() == event.MouseButtonRelease:
                if event.button() == Qt.LeftButton:
                    self.is_resizing = False
                    return True
        return super().eventFilter(obj, event)

    def start_resize(self, event):
        """Start resize operation"""
        self.resize_start_x = event.globalX()
        self.resize_start_y = event.globalY()
        self.resize_start_width = self.width()
        self.resize_start_height = self.height()
        self.is_resizing = True

    def do_resize(self, event):
        """Perform resize operation"""
        # Calculate new size
        new_width = self.resize_start_width + (event.globalX() - self.resize_start_x)
        new_height = self.resize_start_height + (event.globalY() - self.resize_start_y)

        # Apply minimum size constraints
        new_width = max(400, new_width)
        new_height = max(300, new_height)

        # Apply new size
        self.resize(new_width, new_height)

    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget"""
        widget.setToolTip(text)

    def close_application(self):
        """Close application directly when close button is clicked - optimized cleanup with silent error handling"""
        try:
            # Stop all timers for proper cleanup
            timers = ['cursor_timer', 'device_update_timer', 'screen_share_timer', 'memory_cleanup_timer',
                     'stability_timer', 'resource_monitor_timer']
            for timer_name in timers:
                if hasattr(self, timer_name):
                    timer = getattr(self, timer_name)
                    if timer:
                        try:
                            timer.stop()
                        except Exception as e:
                            if not SILENT_ERROR_HANDLING:
                                print(f"❌ Error stopping {timer_name}: {e}")

            # Stop keyboard listener
            if hasattr(self, 'keyboard_listener'):
                try:
                    self.keyboard_listener.stop()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error stopping keyboard listener: {e}")

            # Clean up audio handler
            if hasattr(self, 'audio_handler') and self.audio_handler:
                try:
                    if self.audio_handler.is_listening:
                        self.audio_handler.stop_listening()
                    self.audio_handler.close_audio()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error cleaning up audio handler: {e}")

            # Clean up API client
            if hasattr(self, 'api_client') and self.api_client:
                try:
                    self.api_client.close()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error cleaning up API client: {e}")

            # Force final garbage collection
            gc.collect()

        except Exception as e:
            if not SILENT_ERROR_HANDLING:
                print(f"❌ Error during application cleanup: {e}")
        finally:
            # Always close the application, even if cleanup fails
            try:
                self.close()
            except:
                # Force exit if normal close fails
                import sys
                sys.exit(0)

    def closeEvent(self, event):
        """Handle window close event - optimized cleanup with silent error handling"""
        try:
            # Stop all timers for proper cleanup
            timers = ['cursor_timer', 'device_update_timer', 'screen_share_timer', 'memory_cleanup_timer',
                     'stability_timer', 'resource_monitor_timer']
            for timer_name in timers:
                if hasattr(self, timer_name):
                    timer = getattr(self, timer_name)
                    if timer:
                        try:
                            timer.stop()
                        except Exception as e:
                            if not SILENT_ERROR_HANDLING:
                                print(f"❌ Error stopping {timer_name} in closeEvent: {e}")

            # Stop keyboard listener
            if hasattr(self, 'keyboard_listener'):
                try:
                    self.keyboard_listener.stop()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error stopping keyboard listener in closeEvent: {e}")

            # Clean up audio handler
            if hasattr(self, 'audio_handler') and self.audio_handler:
                try:
                    if self.audio_handler.is_listening:
                        self.audio_handler.stop_listening()
                    self.audio_handler.close_audio()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error cleaning up audio handler in closeEvent: {e}")

            # Clean up API client
            if hasattr(self, 'api_client') and self.api_client:
                try:
                    self.api_client.close()
                except Exception as e:
                    if not SILENT_ERROR_HANDLING:
                        print(f"❌ Error cleaning up API client in closeEvent: {e}")

            # Force final garbage collection
            gc.collect()

        except Exception as e:
            if not SILENT_ERROR_HANDLING:
                print(f"❌ Error during closeEvent cleanup: {e}")
        finally:
            # Always accept the close event
            event.accept()

    # Add mouse event handling for window dragging and click-through
    def mousePressEvent(self, event):
        """Handle mouse press for window dragging and special click behaviors"""
        if event.button() == Qt.LeftButton:
            # Get click position relative to window
            click_pos = event.pos()

            # Define top area for click-to-minimize (top 30 pixels)
            top_area_height = 30

            # Check if click is in the top area for moving window to back
            if click_pos.y() <= top_area_height:
                print("🔄 Click in top area - Moving window to back")
                self.lower()  # Move window to back
                return

            # For dragging, allow on non-interactive areas
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

        elif event.button() == Qt.RightButton:
            # Handle right-click for context menu
            super().mousePressEvent(event)
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def is_interactive_widget(self, widget):
        """Check if a widget is interactive (buttons, dropdowns, etc.)"""
        if widget is None:
            return False

        # List of interactive widget types
        interactive_types = (QPushButton, QComboBox)

        # Check if widget or its parent is interactive
        current_widget = widget
        while current_widget is not None:
            if isinstance(current_widget, interactive_types):
                return True
            if current_widget == self.resize_handle:
                return True
            # The AI response text widget should be non-interactive for click-through
            if current_widget == self.ai_response_text_widget:
                return False
            current_widget = current_widget.parent()

        return False

    def contextMenuEvent(self, event):
        """Handle right-click context menu"""
        context_menu = QMenu(self)

        # Add click-through toggle action
        click_through_action = QAction("Disable Click-Through" if self.click_through_enabled else "Enable Click-Through", self)
        click_through_action.triggered.connect(self.toggle_click_through)
        context_menu.addAction(click_through_action)

        # Add window visibility action
        visibility_action = QAction("Hide Window", self)
        visibility_action.triggered.connect(self.hide_window)
        context_menu.addAction(visibility_action)

        # Add separator
        context_menu.addSeparator()

        # Add help action
        help_action = QAction("Show Controls", self)
        help_action.triggered.connect(self.show_controls_help)
        context_menu.addAction(help_action)

        # Show context menu
        context_menu.exec_(event.globalPos())

    def show_controls_help(self):
        """Show controls help dialog"""
        help_text = """Interview Assistant Controls:

🎯 Window Management:
• Ctrl+Space: Hide/Show window
• Ctrl+Arrow keys: Move window
• Click top area: Move window to back
• Right-click: Context menu
• Drag resize handle: Resize window

🎤 Audio Controls:
• 🤖 FULLY AUTOMATIC MODE - No manual controls needed
• Just speak and get instant responses!

🔧 Advanced Features:
• Click-through mode: ENABLED by default
  - Clicks pass through to underlying apps
  - Buttons/controls still work normally
• Window transparency and stealth features
• Screen capture hiding

💡 Usage Tips:
• Click anywhere on text area → passes to Google Meet
• Use buttons normally → they always work
• Right-click to disable click-through if needed"""

        QMessageBox.information(self, "Controls Help", help_text)

if __name__ == "__main__":
    # Create QApplication
    qt_app = QApplication(sys.argv)
    qt_app.setQuitOnLastWindowClosed(True)

    # Set application properties
    qt_app.setApplicationName("Real-time Interview Assistant")
    qt_app.setApplicationVersion("1.0")

    # Create and show main window
    app = InterviewApp()

    # Add tooltip for resize handle
    app.create_tooltip(app.resize_handle, "🤖 FULLY AUTOMATIC MODE\nDrag to resize window\nCtrl+Arrow keys: Move window\nCtrl+Space: Hide/Show window\nJust speak for instant responses!\nClick top area: Move to back\nClick-through: Enabled by default\nRight-click: Context menu")

    app.show()

    print("🤖 Real-time Interview Assistant Started!")
    print("🥷 Stealth Features:")
    print(f"   • Window transparency: {WINDOW_TRANSPARENCY}")
    print(f"   • Screen capture hiding: {ENABLE_SCREEN_CAPTURE_HIDING}")
    print(f"   • Taskbar hiding: {ENABLE_TASKBAR_HIDING}")
    print("📋 Controls:")
    print("   • 🤖 FULLY AUTOMATIC MODE - Just speak!")
    print("   • Ctrl+Arrow keys: Move window")
    print("   • Ctrl+Space: Hide/Show window")
    print("   • Click top area: Move window to back")
    print("   • Click-through: Enabled by default")
    print("   • Right-click: Context menu to toggle features")
    print("   • Drag resize handle to resize window")

    # Only run mainloop if app initialization was successful (API key loaded)
    if hasattr(app, 'api_client') and app.api_client:
        sys.exit(qt_app.exec_())
    else:
        print("Application failed to initialize due to API key error.")
        sys.exit(1)