import requests
import json
import time
import hashlib
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
try:
    from config import GOOGLE_GEMINI_API_KEY, API_KEYS, API_FALLBACK_ORDER, API_ENDPOINTS
except ImportError:
    print("Error: Could not import API configuration from config.py.")
    print("Please ensure config.py exists and contains your API keys.")
    GOOGLE_GEMINI_API_KEY = None
    API_KEYS = {}
    API_FALLBACK_ORDER = ['gemini']
    API_ENDPOINTS = {}

# Import stealth configuration for silent error handling
try:
    from stealth_config import SILENT_ERROR_HANDLING, INTERVIEW_SAFETY_MODE
except ImportError:
    SILENT_ERROR_HANDLING = True
    INTERVIEW_SAFETY_MODE = True

class APIClient:
    def __init__(self, ai_indicator_callback=None):
        # Validate API keys with silent handling for interview safety
        if not API_KEYS or not any(API_KEYS.values()):
            if SILENT_ERROR_HANDLING:
                print("⚠️ No API keys configured - running in limited mode")
                # Continue with limited functionality instead of raising error
                self.limited_mode = True
            else:
                raise ValueError("No API keys configured. Please check config.py.")
        else:
            self.limited_mode = False

        # Initialize optimized HTTP session with connection pooling and keep-alive
        self.session = requests.Session()

        # Current API being used
        self.current_api = None
        self.failed_apis = set()  # Track failed APIs to avoid retrying in same session

        # Callback to update AI indicator in UI
        self.ai_indicator_callback = ai_indicator_callback

        # OPTIMIZED HTTP adapter for low-end systems
        retry_strategy = Retry(
            total=1,  # Minimal retries for maximum speed
            backoff_factor=0.1,  # Slightly slower backoff for stability
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(
            pool_connections=3,   # Reduced for low-end systems
            pool_maxsize=5,       # Smaller pool for memory efficiency
            max_retries=retry_strategy,
            pool_block=False     # Don't block when pool is full
        )
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)

        # OPTIMIZED headers for maximum speed
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive',
            'Keep-Alive': 'timeout=60, max=200',  # Longer keep-alive for better reuse
            'Accept-Encoding': 'gzip, deflate',   # Enable compression
            'User-Agent': 'InterviewAssistant/1.0'  # Custom user agent
        })

        # Response cache for identical questions (optimized for low-end systems)
        self.response_cache = {}
        self.cache_max_size = 20  # Reduced cache size for memory efficiency
        self.cache_ttl = 300  # 5 minutes TTL

        # Frontend Developer Interview Assistant
        self.system_prompt = """
You are an experienced frontend Angular developer attending a job interview.
Your responsibility is to answer every question confidently, clearly, and accurately — even if the question is unclear, incomplete, or vague.
If a question is not well-formed, do not say it is unclear. Do not include any language like “I believe the interviewer meant...”, “They might be asking about...”, or “Assuming the question is about...”. Do not try to explain what the question could be.
Just analyze silently, infer the intent internally, and respond with a direct, correct, and technically precise answer based on real-world Angular experience.
Speak in first person as the candidate (e.g., “I have worked with...”, “In my last project...”)
Focus on Angular, TypeScript, RxJS, NgRx, frontend tools, and professional best practices. Give answers in full sentences with relevant technical detail, code examples if helpful, and avoid all filler or commentary about the question or answer style.
Keep the tone professional, confident, and focused. Never include meta-comments, small talk, or explanations about how you interpreted the question.

"""
        # Pre-warm connection with a lightweight request
        self._warm_connection()

    def get_next_available_api(self):
        """Get the next available API from fallback order"""
        for api_name in API_FALLBACK_ORDER:
            if api_name not in self.failed_apis and api_name in API_KEYS and API_KEYS[api_name]:
                return api_name
        return None

    def reset_failed_apis(self):
        """Reset failed APIs list - useful for new sessions"""
        self.failed_apis.clear()
        print("🔄 Reset API fallback system")

    def _get_ai_display_name(self, api_name):
        """Get formatted display name for AI"""
        ai_names = {
            'gemini': 'Gemini',
            'mistral': 'Mistral',
            'openrouter': 'OpenRouter',
            'openai': 'OpenAI'
        }
        return ai_names.get(api_name, api_name.title())

    def call_gemini_api(self, payload):
        """Call Gemini API"""
        api_key = API_KEYS.get('gemini')
        if not api_key:
            raise Exception("Gemini API key not found")

        url = f"{API_ENDPOINTS['gemini']}?key={api_key}"
        response = self.session.post(url, data=json.dumps(payload), timeout=5.0)  # Increased timeout for low-end systems
        response.raise_for_status()
        return response.json()

    def call_mistral_api(self, payload):
        """Call Mistral API"""
        api_key = API_KEYS.get('mistral')
        if not api_key:
            raise Exception("Mistral API key not found")

        # Convert Gemini format to Mistral format
        mistral_payload = {
            "model": "mistral-small",
            "messages": [{"role": "user", "content": payload["contents"][0]["parts"][0]["text"]}],
            "max_tokens": payload["generationConfig"]["maxOutputTokens"],
            "temperature": payload["generationConfig"]["temperature"]
        }

        headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        response = self.session.post(API_ENDPOINTS['mistral'],
                                   data=json.dumps(mistral_payload),
                                   headers=headers, timeout=5.0)  # Increased timeout for low-end systems
        response.raise_for_status()

        # Convert Mistral response to Gemini format
        mistral_response = response.json()
        return {
            "candidates": [{"content": {"parts": [{"text": mistral_response["choices"][0]["message"]["content"]}]}}]
        }

    def call_openrouter_api(self, payload):
        """Call OpenRouter API"""
        api_key = API_KEYS.get('openrouter')
        if not api_key:
            raise Exception("OpenRouter API key not found")

        # Convert Gemini format to OpenRouter format
        openrouter_payload = {
            "model": "google/gemini-flash-1.5",
            "messages": [{"role": "user", "content": payload["contents"][0]["parts"][0]["text"]}],
            "max_tokens": payload["generationConfig"]["maxOutputTokens"],
            "temperature": payload["generationConfig"]["temperature"]
        }

        headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        response = self.session.post(API_ENDPOINTS['openrouter'],
                                   data=json.dumps(openrouter_payload),
                                   headers=headers, timeout=5.0)  # Increased timeout for low-end systems
        response.raise_for_status()

        # Convert OpenRouter response to Gemini format
        openrouter_response = response.json()
        return {
            "candidates": [{"content": {"parts": [{"text": openrouter_response["choices"][0]["message"]["content"]}]}}]
        }

    def call_openai_api(self, payload):
        """Call OpenAI API"""
        api_key = API_KEYS.get('openai')
        if not api_key:
            raise Exception("OpenAI API key not found")

        # Convert Gemini format to OpenAI format
        openai_payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": payload["contents"][0]["parts"][0]["text"]}],
            "max_tokens": payload["generationConfig"]["maxOutputTokens"],
            "temperature": payload["generationConfig"]["temperature"]
        }

        headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        response = self.session.post(API_ENDPOINTS['openai'],
                                   data=json.dumps(openai_payload),
                                   headers=headers, timeout=5.0)  # Increased timeout for low-end systems
        response.raise_for_status()

        # Convert OpenAI response to Gemini format
        openai_response = response.json()
        return {
            "candidates": [{"content": {"parts": [{"text": openai_response["choices"][0]["message"]["content"]}]}}]
        }

    def _warm_connection(self):
        """Pre-warm the HTTP connection to reduce first request latency."""
        try:
            # Make a minimal request to establish connection
            warm_payload = {
                "contents": [{"parts": [{"text": "Hi"}]}],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 10
                }
            }
            # Use a very short timeout for warming
            self.session.post(GEMINI_API_URL, data=json.dumps(warm_payload), timeout=2)
        except:
            # Ignore warming failures - connection will be established on first real request
            pass

    def _get_cache_key(self, text):
        """Generate context-aware cache key for the given text"""
        # Normalize text for better matching
        normalized_text = text.lower().strip()

        # Remove common filler words that don't affect meaning
        filler_words = ['um', 'uh', 'er', 'ah', 'like', 'you know', 'so', 'well']
        words = normalized_text.split()
        filtered_words = [word for word in words if word not in filler_words]

        # Create key from filtered, normalized text
        key_text = ' '.join(filtered_words)
        return hashlib.md5(key_text.encode()).hexdigest()

    def _is_cache_valid(self, timestamp):
        """Check if cache entry is still valid"""
        return time.time() - timestamp < self.cache_ttl

    def _clean_cache(self):
        """Remove expired entries and maintain cache size limit"""
        current_time = time.time()
        # Remove expired entries
        expired_keys = [k for k, (_, timestamp) in self.response_cache.items()
                       if current_time - timestamp >= self.cache_ttl]
        for key in expired_keys:
            del self.response_cache[key]

        # Maintain size limit (remove oldest entries)
        if len(self.response_cache) > self.cache_max_size:
            sorted_items = sorted(self.response_cache.items(),
                                key=lambda x: x[1][1])  # Sort by timestamp
            items_to_remove = len(self.response_cache) - self.cache_max_size
            for i in range(items_to_remove):
                del self.response_cache[sorted_items[i][0]]

    def get_ai_response(self, transcribed_text, streaming_callback=None):
        """Sends transcribed text to Google Gemini API and gets a response with optimizations."""
        if not transcribed_text:
            return "(Gemini) Answer:\nNo transcription provided to AI."

        # Handle limited mode silently
        if hasattr(self, 'limited_mode') and self.limited_mode:
            if SILENT_ERROR_HANDLING:
                return "(Gemini) Answer:\nI'm ready to help with your interview questions."
            else:
                return "(Gemini) Answer:\nAPI configuration error. Please check your settings."

        # Performance optimization: Check cache first
        cache_key = self._get_cache_key(transcribed_text)
        if cache_key in self.response_cache:
            cached_response, timestamp = self.response_cache[cache_key]
            if self._is_cache_valid(timestamp):
                print(f"Cache hit for question: {transcribed_text[:50]}...")
                # If streaming callback provided, simulate streaming for cached response
                if streaming_callback:
                    self._simulate_streaming(cached_response, streaming_callback)
                return cached_response

        # Clean cache periodically
        self._clean_cache()

        # Voice-friendly interview prompt
        interview_prompt = f"""{self.system_prompt}

INTERVIEWER'S QUESTION: "{transcribed_text}"

CRITICAL INSTRUCTIONS - FOLLOW EXACTLY:
- NEVER EVER say "there is no concept called", "isn't a standard", "I believe you meant", "you might be asking", "assuming you're referring", "it's possible you're referring", "however I believe", "I think you mean", "perhaps you're asking", "it seems like", "I assume you're asking", "you are referring to"
- NEVER provide ANY explanatory text about the question being unclear
- NEVER mention that the question is vague or needs clarification
- If the question has a typo (like "director" instead of "directive"), answer about the correct concept WITHOUT mentioning the mistake
- START IMMEDIATELY with technical information - NO explanatory phrases
- Answer in simple Indian-style English
- For technical questions, follow: Direct Answer + Code Example + Bonus Tip
- Assume you understand the question perfectly and respond accordingly

EXAMPLES OF WHAT NOT TO SAY:
❌ "There is no concept called actual word. However, I believe you're asking about..."
❌ "It seems like you're asking about Angular directives..."
❌ "I assume you're asking about..."
❌ "You might be referring to..."

CORRECT APPROACH:
✅ "Directives in Angular are classes that add behavior to DOM elements..."
✅ "I have extensive experience with Angular directives..."

PROVIDE YOUR DIRECT ANSWER NOW:"""
        print(f"DEBUG: Sending to AI - Question: {transcribed_text}") # Log the question

        # Optimized payload for code examples - balanced token count
        payload = {
            "contents": [{"parts": [{"text": interview_prompt}]}],
            "generationConfig": {
                "temperature": 0.1,      # Lower temperature for more confident, consistent responses
                "topK": 1,               # Single best choice for confidence
                "topP": 0.8,             # High probability mass for quality responses
                "maxOutputTokens": 200,  # Increased tokens to accommodate code examples
                "candidateCount": 1      # Single candidate for speed
            }
        }

        # Try APIs in fallback order: Gemini > Mistral > OpenRouter > OpenAI
        response_json = None
        used_api = None

        for api_name in API_FALLBACK_ORDER:
            if api_name in self.failed_apis:
                continue

            try:
                # Update UI indicator to show which API is processing
                if self.ai_indicator_callback:
                    self.ai_indicator_callback(api_name, "processing")

                # Performance timing for monitoring
                start_time = time.time()
                print(f"Trying {api_name.upper()}: {transcribed_text[:50]}...")

                # Call appropriate API
                if api_name == 'gemini':
                    response_json = self.call_gemini_api(payload)
                elif api_name == 'mistral':
                    response_json = self.call_mistral_api(payload)
                elif api_name == 'openrouter':
                    response_json = self.call_openrouter_api(payload)
                elif api_name == 'openai':
                    response_json = self.call_openai_api(payload)

                response_time = time.time() - start_time
                print(f"✅ {api_name.upper()} response time: {response_time:.3f}s")
                used_api = api_name
                break  # Success! Exit the loop

            except Exception as e:
                print(f"❌ {api_name.upper()} failed: {str(e)}")
                self.failed_apis.add(api_name)
                continue  # Try next API

        if response_json is None:
            if SILENT_ERROR_HANDLING:
                # Return a helpful response instead of error message during interviews
                return "(Gemini) Answer:\nI understand your question. Let me think about the best approach to answer this technical question based on my experience."
            else:
                return "(Gemini) Answer:\nAll APIs failed. Please check your internet connection and API keys."

        try:

            # Enhanced response extraction with validation
            if 'candidates' in response_json and response_json['candidates']:
                candidate = response_json['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content'] and candidate['content']['parts']:
                    ai_text = candidate['content']['parts'][0]['text'].strip()

                    # Validate response quality
                    is_valid, validation_message = self._validate_response_quality(transcribed_text, ai_text)

                    if is_valid:
                        print(f"✅ {used_api.upper()} Success: {ai_text[:50]}...")

                        # Format response with AI name
                        ai_name_formatted = self._get_ai_display_name(used_api)
                        formatted_response = f"({ai_name_formatted}) Answer:\n{ai_text}"

                        # Cache the validated response
                        self.response_cache[cache_key] = (formatted_response, time.time())

                        # Reset AI indicator to ready state
                        if self.ai_indicator_callback:
                            self.ai_indicator_callback(used_api, "ready")

                        # If streaming callback provided, stream the response AFTER caching
                        if streaming_callback:
                            self._simulate_streaming(formatted_response, streaming_callback)

                        return formatted_response
                    else:
                        print(f"❌ Response validation failed: {validation_message}")
                        print(f"❌ Invalid response: {ai_text}")

                        # Try to regenerate response with more specific prompt
                        return self._regenerate_response(transcribed_text, ai_text, validation_message)
                elif 'finishReason' in candidate and candidate['finishReason'] != 'STOP':
                    return f"[AI Error: Generation stopped due to {candidate['finishReason']}]"

            # Optimized error handling
            error_message = response_json.get('error', {}).get('message', 'Unknown error')
            if 'promptFeedback' in response_json and 'blockReason' in response_json['promptFeedback']:
                 error_message = f"Content blocked: {response_json['promptFeedback']['blockReason']}"
            print(f"Gemini API error: {error_message}")
            return f"[AI Error: {error_message}]"

        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error: {http_err}")
            if SILENT_ERROR_HANDLING:
                return "I'm processing your question. Could you please rephrase it or provide more context?"
            else:
                return f"[API HTTP Error: {http_err}]"
        except requests.exceptions.Timeout:
            print("Request timeout - API took too long to respond")
            if SILENT_ERROR_HANDLING:
                return "I need a moment to process that question. Could you please repeat it?"
            else:
                return "[API Timeout: Response took too long]"
        except requests.exceptions.RequestException as req_err:
            print(f"Request error: {req_err}")
            if SILENT_ERROR_HANDLING:
                return "I'm having trouble processing that question right now. Could you try asking it differently?"
            else:
                return f"[API Request Error: {req_err}]"
        except Exception as e:
            print(f"Unexpected error calling Gemini API: {e}")
            if SILENT_ERROR_HANDLING:
                return "I apologize, but I need you to repeat the question. I want to make sure I give you the best answer."
            else:
                return f"[Unexpected API Error: {e}]"

    def _simulate_streaming(self, full_response, callback):
        """Simulate streaming by sending response word by word to callback."""
        import threading
        import time

        def stream_words():
            words = full_response.split()
            current_text = ""

            for i, word in enumerate(words):
                current_text += word + " "
                # Call the callback with partial response
                try:
                    callback(current_text.strip())
                except:
                    # If callback fails, continue streaming
                    pass

                # ULTRA-FAST streaming for immediate visibility
                if i < 3:  # First 3 words instantly
                    time.sleep(0.01)  # 10ms for immediate start
                elif i < 8:  # Next 5 words very fast
                    time.sleep(0.015)  # 15ms
                else:
                    time.sleep(0.025)  # 25ms for remaining words

        # Run streaming in separate thread to not block
        threading.Thread(target=stream_words, daemon=True).start()

    def _validate_response_quality(self, question, response):
        """Validate that the response is appropriate for the technical question"""
        if not response or len(response.strip()) < 10:
            return False, "Response too short"

        question_lower = question.lower()
        response_lower = response.lower()

        # Check if response mentions being an AI (should not happen with new prompt)
        ai_mentions = [' i am an ai', ' as an ai', ' i\'m an ai', ' artificial intelligence', ' language model', 'i am ai', 'as ai']
        if any(mention in response_lower for mention in ai_mentions):
            return False, "Response mentions being an AI"

        # Handle general programming questions (Python, Java, etc.)
        general_programming_keywords = ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift']
        if any(keyword in question_lower for keyword in general_programming_keywords):
            # For general programming questions, just check for basic quality
            if len(response.split()) < 10:  # Should have at least 10 words
                return False, "Programming response too brief"
            return True, "General programming response validated"

        # Frontend-specific concept validation
        frontend_checks = [
            # React vs Angular confusion
            {
                'question_keywords': ['react', 'jsx', 'hooks', 'props', 'state'],
                'wrong_response_keywords': ['angular', 'directive', 'service', 'module', 'ngmodule'],
                'correct_keywords': ['component', 'jsx', 'hooks', 'props', 'state', 'react'],
                'error': "Confused React concepts with Angular"
            },
            # Angular vs React confusion
            {
                'question_keywords': ['angular', 'directive', 'service', 'module', 'ngmodule'],
                'wrong_response_keywords': ['react', 'jsx', 'hooks', 'props'],
                'correct_keywords': ['component', 'directive', 'service', 'module', 'angular'],
                'error': "Confused Angular concepts with React"
            },
            # JavaScript hoisting vs web hosting
            {
                'question_keywords': ['hoisting', 'hoist'],
                'wrong_response_keywords': ['hosting', 'server', 'website', 'domain'],
                'correct_keywords': ['variable', 'function', 'scope', 'declaration', 'javascript'],
                'error': "Confused JavaScript hoisting with web hosting"
            },
            # Closures validation
            {
                'question_keywords': ['closure', 'closures'],
                'wrong_response_keywords': ['enclosure', 'closing', 'close'],
                'correct_keywords': ['function', 'scope', 'variable', 'outer', 'inner'],
                'error': "Incorrect closure explanation"
            },
            # TypeScript validation
            {
                'question_keywords': ['typescript', 'interface', 'type'],
                'wrong_response_keywords': ['javascript only'],
                'correct_keywords': ['type', 'interface', 'typescript', 'static'],
                'error': "Incorrect TypeScript explanation"
            }
        ]

        for check in frontend_checks:
            # Check if question is about this concept
            if any(keyword in question_lower for keyword in check['question_keywords']):
                # Check if response contains wrong keywords
                wrong_count = sum(1 for keyword in check['wrong_response_keywords'] if keyword in response_lower)
                if wrong_count > 0:
                    # Verify it has enough correct keywords to compensate
                    correct_count = sum(1 for keyword in check['correct_keywords'] if keyword in response_lower)
                    if correct_count < 2:  # Need at least 2 correct keywords
                        return False, check['error']

        # Check for reasonable length (not too short for technical questions)
        if any(tech in question_lower for tech in ['react', 'angular', 'javascript', 'typescript', 'component']):
            if len(response.split()) < 15:  # Technical answers should be substantial
                return False, "Technical response too brief"

        return True, "Response validated"

    def _regenerate_response(self, question, invalid_response, error_reason):
        """Regenerate response with more specific instructions to fix errors"""
        print(f"🔄 Regenerating response due to: {error_reason}")

        # Create more specific prompt based on the error
        correction_prompt = f"""{self.system_prompt}

CRITICAL: The previous response was incorrect due to: {error_reason}

INTERVIEWER'S QUESTION: "{question}"

PREVIOUS INCORRECT RESPONSE: "{invalid_response}"

STRICT INSTRUCTIONS FOR REGENERATION:
1. The previous response was REJECTED for containing explanatory text
2. You MUST provide a DIRECT technical answer with NO explanatory phrases
3. NEVER EVER use: "there is no concept called", "I believe you meant", "you might be asking", "assuming you're referring", "it's possible", "however", "it seems like", "I assume", "you are referring to"
4. START IMMEDIATELY with technical information
5. Answer as an experienced Angular developer
6. If the question has a typo, answer about the correct concept WITHOUT mentioning the mistake

DIRECT TECHNICAL ANSWER (NO explanatory text, NO disclaimers):"""

        payload = {
            "contents": [{"parts": [{"text": correction_prompt}]}],
            "generationConfig": {
                "temperature": 0.1,      # Lower temperature for more focused response
                "topK": 1,               # Single choice for consistency
                "topP": 0.5,             # More focused
                "maxOutputTokens": 180,
                "candidateCount": 1
            }
        }

        try:
            # Try to regenerate using the same API fallback system
            response_json = None
            for api_name in API_FALLBACK_ORDER:
                if api_name in self.failed_apis:
                    continue
                try:
                    if api_name == 'gemini':
                        response_json = self.call_gemini_api(payload)
                    elif api_name == 'mistral':
                        response_json = self.call_mistral_api(payload)
                    elif api_name == 'openrouter':
                        response_json = self.call_openrouter_api(payload)
                    elif api_name == 'openai':
                        response_json = self.call_openai_api(payload)
                    break
                except Exception as e:
                    print(f"❌ {api_name.upper()} failed during regeneration: {str(e)}")
                    continue

            if response_json is None:
                raise Exception("All APIs failed during regeneration")

            if 'candidates' in response_json and response_json['candidates']:
                candidate = response_json['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content'] and candidate['content']['parts']:
                    corrected_text = candidate['content']['parts'][0]['text'].strip()

                    # Validate the corrected response
                    is_valid, validation_message = self._validate_response_quality(question, corrected_text)

                    if is_valid:
                        print(f"✓ Corrected response validated: {corrected_text}")
                        return corrected_text
                    else:
                        print(f"❌ Corrected response still invalid: {validation_message}")
                        # Return a direct technical answer instead of asking for clarification
                        return "Directives in Angular are classes that add behavior to DOM elements. There are three types: component directives (reusable UI components), attribute directives (modify element behavior like ngClass), and structural directives (change DOM layout like *ngIf). I have extensive experience implementing custom directives for dynamic UI interactions."

            return "In Angular development, I focus on building scalable applications using TypeScript, RxJS for reactive programming, and NgRx for state management. I have experience with component architecture, dependency injection, and optimizing performance through OnPush change detection strategy."

        except Exception as e:
            print(f"Error during response regeneration: {e}")
            return "I have strong experience with Angular development, including component lifecycle hooks, reactive forms, HTTP client for API integration, and implementing responsive designs with Angular Material. I'm confident in my ability to contribute effectively to frontend projects."

    def close(self):
        """Clean up resources"""
        if hasattr(self, 'session'):
            self.session.close()

# Example usage (for testing api_client.py directly)
if __name__ == '__main__':
    if GOOGLE_GEMINI_API_KEY:
        client = APIClient()
        sample_transcription = "Can you tell me about your experience with Python and web development?"
        print(f"Testing AI response for: {sample_transcription}")
        response = client.get_ai_response(sample_transcription)
        print(f"\nFormatted AI Response:\n{response}")

        sample_transcription_2 = "What are your salary expectations?"
        print(f"\nTesting AI response for: {sample_transcription_2}")
        response_2 = client.get_ai_response(sample_transcription_2)
        print(f"\nFormatted AI Response:\n{response_2}")
    else:
        print("Cannot run APIClient test because GOOGLE_GEMINI_API_KEY is not set in config.py.")