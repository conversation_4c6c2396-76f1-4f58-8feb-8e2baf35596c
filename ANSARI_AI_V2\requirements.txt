# Universal dependencies - Compatible with Python 3.7+
# Core GUI and Application Framework
PyQt5>=5.15.0,<6.0.0

# Audio Processing (Universal compatibility)
pyaudio>=0.2.11
SpeechRecognition>=3.8.0

# HTTP and Network
requests>=2.25.0
urllib3>=1.26.0

# Input/Output Control
pynput>=1.7.0

# System Monitoring and Control
psutil>=5.8.0

# Windows-specific audio control (only on Windows)
pycaw>=20220416; sys_platform == "win32"
comtypes>=1.1.10; sys_platform == "win32"

# Build dependencies
PyInstaller>=5.0.0
setuptools>=50.0.0
wheel>=0.36.0

# Python version compatibility helpers
importlib-metadata>=4.0.0; python_version < "3.8"
typing-extensions>=3.10.0; python_version < "3.8"