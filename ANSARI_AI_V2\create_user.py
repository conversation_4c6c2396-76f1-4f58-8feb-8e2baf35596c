#!/usr/bin/env python3
"""
User Creation Script for AI Assistant
Creates new users with 5 minutes default time allocation
"""

import sys
import re
from datetime import datetime
from firebase_config import FirebaseManager

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def create_user():
    """Main function to create a new user"""
    print("=" * 60)
    print("🚀 AI Assistant - User Creation Tool")
    print("=" * 60)
    print()
    
    # Initialize Firebase
    print("🔄 Initializing Firebase connection...")
    firebase_manager = FirebaseManager()
    
    if not firebase_manager.db:
        print("❌ Failed to connect to Firebase!")
        print("Please check your internet connection and try again.")
        input("\nPress Enter to exit...")
        return False
    
    print("✅ Firebase connected successfully!")
    print()
    
    while True:
        # Get email from user
        print("📧 Enter user email address:")
        email = input("Email: ").strip().lower()
        
        if not email:
            print("❌ Email cannot be empty!")
            continue
            
        if not validate_email(email):
            print("❌ Invalid email format! Please enter a valid email address.")
            continue
            
        # Check if user already exists
        print(f"🔍 Checking if user {email} already exists...")
        
        try:
            user_ref = firebase_manager.db.collection('users').document(email)
            user_doc = user_ref.get()
            
            if user_doc.exists:
                existing_data = user_doc.to_dict()
                allocated_time = existing_data.get('allocated_time_minutes', 0)
                used_time = existing_data.get('used_time_minutes', 0)
                remaining_time = allocated_time - used_time
                
                print(f"⚠️  User {email} already exists!")
                print(f"   📊 Allocated Time: {allocated_time} minutes")
                print(f"   ⏱️  Used Time: {used_time:.2f} minutes")
                print(f"   ⏳ Remaining Time: {remaining_time:.2f} minutes")
                print()
                
                choice = input("Do you want to:\n1. Create anyway (will overwrite)\n2. Enter different email\n3. Exit\nChoice (1/2/3): ").strip()
                
                if choice == "1":
                    break
                elif choice == "2":
                    continue
                elif choice == "3":
                    print("👋 Goodbye!")
                    return False
                else:
                    print("❌ Invalid choice! Please enter 1, 2, or 3.")
                    continue
            else:
                break
                
        except Exception as e:
            print(f"❌ Error checking user: {e}")
            continue
    
    # Create user with 5 minutes default time
    print(f"🔄 Creating user {email} with 5 minutes allocation...")
    
    try:
        user_data = {
            'email': email,
            'allocated_time_minutes': 5,  # Default 5 minutes
            'used_time_minutes': 0,
            'created_at': datetime.now(),
            'last_login': None,
            'last_used': None,
            'status': 'active',
            'created_by': 'user_creation_script'
        }
        
        # Add user to Firestore
        user_ref = firebase_manager.db.collection('users').document(email)
        user_ref.set(user_data)
        
        print("✅ User created successfully!")
        print()
        print("📋 User Details:")
        print(f"   📧 Email: {email}")
        print(f"   ⏰ Allocated Time: 5 minutes")
        print(f"   📅 Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   🟢 Status: Active")
        print()
        print("🎉 User can now login to the AI Assistant!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return False

def main():
    """Main entry point"""
    try:
        success = create_user()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ User creation completed successfully!")
        else:
            print("\n" + "=" * 60)
            print("❌ User creation failed!")
            
    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    print("=" * 60)
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
